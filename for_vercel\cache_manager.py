"""
Simplified cache management module for Vercel deployment.
Uses only in-memory caching (no Redis) for serverless compatibility.
"""

import hashlib
from typing import Optional, Any, Dict
import logging
from cachetools import TTLCache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CacheManager:
    """
    Simplified cache manager with in-memory caching only.
    Optimized for Vercel serverless environment with proper TTL handling.
    """

    def __init__(self):
        # Use separate caches for different data types with appropriate TTL
        # Routes and routestops are very static (change ~once per year)
        self.caches = {
            'routes': TTLCache(maxsize=200, ttl=30 * 24 * 3600),     # 30 days - routes rarely change
            'routestops': TTLCache(maxsize=200, ttl=30 * 24 * 3600), # 30 days - route-stop relationships rarely change
            'stops': TTLCache(maxsize=500, ttl=90 * 24 * 3600),      # 90 days - stop info is extremely static
            'eta': TTLCache(maxsize=100, ttl=30),                    # 30 seconds - REAL-TIME!
            'default': TTLCache(maxsize=100, ttl=3600)               # 1 hour default
        }

        # Cache TTL settings (for reference)
        self.cache_ttl = {
            'routes': 30 * 24 * 3600,     # 30 days - routes change very rarely
            'routestops': 30 * 24 * 3600, # 30 days - route-stop relationships change very rarely
            'stops': 90 * 24 * 3600,      # 90 days - stop info is extremely static
            'eta': 30,                    # 30 seconds - ETA data is real-time
            'default': 3600               # 1 hour default
        }

        logger.info("In-memory cache initialized for Vercel with optimized TTL handling")
        logger.info(f"Cache TTL settings:")
        logger.info(f"  - Routes: {self.cache_ttl['routes'] // (24*3600)} days (very static)")
        logger.info(f"  - Route-stops: {self.cache_ttl['routestops'] // (24*3600)} days (very static)")
        logger.info(f"  - Stops: {self.cache_ttl['stops'] // (24*3600)} days (extremely static)")
        logger.info(f"  - ETA: {self.cache_ttl['eta']} seconds (real-time)")

    def _generate_key(self, prefix: str, *args) -> str:
        """Generate a consistent cache key from prefix and arguments."""
        key_data = f"{prefix}:{':'.join(str(arg) for arg in args)}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def get(self, key: str, data_type: str = 'default') -> Optional[Any]:
        """Get value from appropriate cache based on data type."""
        try:
            cache = self.caches.get(data_type, self.caches['default'])
            result = cache.get(key)
            if result is not None and data_type == 'eta':
                logger.debug(f"ETA cache hit for key: {key}")
            return result
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None

    def set(self, key: str, value: Any, data_type: str = 'default') -> bool:
        """Set value in appropriate cache with correct TTL."""
        try:
            cache = self.caches.get(data_type, self.caches['default'])
            cache[key] = value
            if data_type == 'eta':
                logger.debug(f"ETA data cached for {self.cache_ttl['eta']} seconds: {key}")
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """Delete key from all caches."""
        try:
            deleted = False
            for cache_name, cache in self.caches.items():
                if key in cache:
                    del cache[key]
                    deleted = True
            return deleted
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for all cache types."""
        stats = {
            'redis_available': False,
            'cache_type': 'memory_only_multi_ttl',
            'environment': 'vercel_serverless'
        }

        # Add stats for each cache type
        for cache_name, cache in self.caches.items():
            stats[f'{cache_name}_cache_size'] = len(cache)
            stats[f'{cache_name}_cache_maxsize'] = cache.maxsize
            stats[f'{cache_name}_cache_ttl'] = self.cache_ttl.get(cache_name, 'unknown')

        return stats

# Global cache instance
cache_manager = CacheManager()

# Convenience functions for specific data types
def cache_routes(key: str, data: Any) -> bool:
    """Cache route data."""
    return cache_manager.set(key, data, 'routes')

def get_cached_routes(key: str) -> Optional[Any]:
    """Get cached route data."""
    return cache_manager.get(key, 'routes')

def cache_stops(key: str, data: Any) -> bool:
    """Cache stop data."""
    return cache_manager.set(key, data, 'stops')

def get_cached_stops(key: str) -> Optional[Any]:
    """Get cached stop data."""
    return cache_manager.get(key, 'stops')

def cache_eta(key: str, data: Any) -> bool:
    """Cache ETA data."""
    return cache_manager.set(key, data, 'eta')

def get_cached_eta(key: str) -> Optional[Any]:
    """Get cached ETA data."""
    return cache_manager.get(key, 'eta')

def cache_routestops(key: str, data: Any) -> bool:
    """Cache route-stop data."""
    return cache_manager.set(key, data, 'routestops')

def get_cached_routestops(key: str) -> Optional[Any]:
    """Get cached route-stop data."""
    return cache_manager.get(key, 'routestops')