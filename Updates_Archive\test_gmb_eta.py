#!/usr/bin/env python3
"""
Test script for GMB ETA functionality
"""

import requests
import json
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fetch_json_with_retry(url, max_retries=3):
    """Fetch JSON with retry logic."""
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"HTTP {response.status_code} for URL: {url}")
        except Exception as e:
            logger.warning(f"Error fetching {url} (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                import time
                time.sleep(2 ** attempt)  # Exponential backoff

    logger.error(f"Failed to fetch {url} after {max_retries} attempts")
    return None

def load_gmb_stop_eta_optimized(stop_id, route_param):
    """Load GMB stop ETA from API."""
    try:
        # Use route_param as route_id directly for ETA endpoint
        route_id = route_param

        # Get ETA data using route-stop endpoint
        eta_url = f"https://data.etagmb.gov.hk/eta/route-stop/{route_id}/{stop_id}"
        logger.info(f"DEBUG - GMB ETA URL: {eta_url}")
        eta_result = fetch_json_with_retry(eta_url)

        logger.info(f"DEBUG - GMB ETA response status: {eta_result is not None}")
        if eta_result:
            logger.info(f"DEBUG - GMB ETA response keys: {list(eta_result.keys()) if isinstance(eta_result, dict) else 'Not a dict'}")
            logger.info(f"DEBUG - GMB ETA response: {json.dumps(eta_result, indent=2, ensure_ascii=False)}")

        if not eta_result or not eta_result.get('data'):
            logger.warning(f"No GMB ETA data for route {route_param} stop {stop_id}")
            return []

        eta_list = []
        
        # Process each data item (there might be multiple route_seq entries)
        for data_item in eta_result['data']:
            if not data_item.get('enabled', False):
                logger.info(f"GMB route {route_param} stop {stop_id} is disabled")
                continue
                
            eta_entries = data_item.get('eta', [])
            logger.info(f"DEBUG - Found {len(eta_entries)} ETA entries for route {route_param} stop {stop_id}")
            
            for eta_entry in eta_entries:
                time_eta = ''
                eta = ''
                rmk_tc = eta_entry.get('remarks_tc') or '小巴'

                time_current = datetime.strptime(eta_result["generated_timestamp"], '%Y-%m-%dT%H:%M:%S+08:00')
                
                if eta_entry.get("timestamp"):
                    time_eta = datetime.strptime(eta_entry["timestamp"], '%Y-%m-%dT%H:%M:%S+08:00')
                    delta = time_eta - time_current
                    minutes, seconds = divmod(delta.total_seconds(), 60)
                    eta = f"{int(minutes)} minutes {int(seconds)} seconds"
                    
                    if delta.total_seconds() < 0:
                        rmk_tc = "已到達"
                    elif delta.total_seconds() < 60:
                        eta = "即將到達"
                        
                    dt_current_str = time_current.strftime("%Y-%m-%d %H:%M:%S")
                    eta_obj = [time_eta, eta, rmk_tc, dt_current_str]
                    eta_list.append(eta_obj)

        logger.info(f"DEBUG - Returning {len(eta_list)} ETA entries for GMB route {route_param} stop {stop_id}")
        return eta_list

    except Exception as e:
        logger.error(f"Error in load_gmb_stop_eta_optimized: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("Testing GMB ETA function...")
    print("=" * 50)
    
    # Test with your specific values
    stop_id = "20001219"
    route_id = "2005253"
    
    print(f"Testing with stop_id={stop_id}, route_id={route_id}")
    print()
    
    result = load_gmb_stop_eta_optimized(stop_id, route_id)
    
    print()
    print("=" * 50)
    print("FINAL RESULT:")
    print(f"Type: {type(result)}")
    print(f"Length: {len(result) if result else 'None'}")
    print(f"Content: {result}")
    
    if result:
        print("\nFormatted ETA entries:")
        for i, eta_obj in enumerate(result, 1):
            print(f"  {i}. Time: {eta_obj[1]}, Remarks: {eta_obj[2]}, Updated: {eta_obj[3]}")
