"""
Test script for KV cache functionality.
This script can be used to test the KV cache implementation locally.
"""

import os
import sys
import logging

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kv_cache import kv_cache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_kv_cache():
    """Test KV cache functionality."""
    print("🧪 Testing KV Cache Implementation")
    print("=" * 50)
    
    # Test 1: Check KV availability
    print("\n1. Testing KV availability...")
    is_available = kv_cache.is_available()
    print(f"   KV Available: {is_available}")
    
    if not is_available:
        print("   ⚠️ KV not available - this is expected in local development")
        print("   ✅ Fallback behavior will be tested")
    
    # Test 2: Try to get GMB routes (should return None if no cache)
    print("\n2. Testing GMB routes retrieval...")
    gmb_routes = kv_cache.get_gmb_routes()
    if gmb_routes:
        print(f"   ✅ Found {len(gmb_routes)} GMB routes in cache")
    else:
        print("   ❌ No GMB routes in cache (expected for fresh setup)")
    
    # Test 3: Test cache info
    print("\n3. Testing cache info...")
    cache_info = kv_cache.get_cache_info()
    print(f"   Cache Info: {cache_info}")
    
    # Test 4: Test with sample data (only if KV is available)
    if is_available:
        print("\n4. Testing cache storage...")
        sample_routes = [
            {
                "route": "1",
                "co": "GMB",
                "region": "HKI",
                "dest_tc": "測試路線"
            }
        ]
        
        success = kv_cache.set_gmb_routes(sample_routes)
        if success:
            print("   ✅ Sample data stored successfully")
            
            # Try to retrieve it
            retrieved = kv_cache.get_gmb_routes()
            if retrieved and len(retrieved) == 1:
                print("   ✅ Sample data retrieved successfully")
            else:
                print("   ❌ Failed to retrieve sample data")
        else:
            print("   ❌ Failed to store sample data")
    
    print("\n" + "=" * 50)
    print("🏁 KV Cache test completed")

if __name__ == "__main__":
    test_kv_cache()
