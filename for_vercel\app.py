"""
Vercel entry point for the optimized bus route Flask application.
This file adapts app_fixed.py for Vercel's serverless environment.
"""

# No need to modify sys.path since all files are in the same directory

from flask import Flask, jsonify, request
from datetime import datetime
from math import radians, sin, cos, sqrt, atan2
import logging
import time
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

# Import our caching module
from cache_manager import (
    cache_manager, cache_routes, get_cached_routes,
    cache_stops, get_cached_stops, cache_eta, get_cached_eta,
    cache_routestops, get_cached_routestops
)

# Import KV cache for GMB routes
from kv_cache import kv_cache

# Configure logging for Vercel
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Global variables
route = ''
bound = ''

# Cache for CTB data
ctb_routes_cache = None
ctb_cache_timestamp = None

# Cache for GMB data
gmb_routes_cache = None
gmb_cache_timestamp = None

# Create a regular requests session (no file-based caching for Vercel)
http_session = requests.Session()

# Configure session with optimizations
http_session.timeout = 10

# GMB API Configuration
GMB_REGIONS = ["HKI", "KLN", "NT"]  # Hong Kong Island, Kowloon, New Territories

def fetch_ctb_routes_from_api():
    """Fetch CTB routes from the official API with Vercel-optimized timeout."""
    try:
        logger.info("Fetching CTB routes from API...")
        url = "https://rt.data.gov.hk/v2/transport/citybus/route/ctb"
        result = fetch_json_with_retry(url, max_retries=1, timeout=8)  # Faster for Vercel

        if result and result.get('data'):
            ctb_routes = result['data']
            # Add company identifier for consistency
            for route in ctb_routes:
                route["co"] = "CTB"

            logger.info(f"Successfully fetched {len(ctb_routes)} CTB routes from API")
            return ctb_routes
        else:
            logger.error("Failed to fetch CTB routes from API - no data")
            return []

    except Exception as e:
        logger.error(f"Error fetching CTB routes from API: {e}")
        return []

def load_ctb_data_from_api():
    """Load CTB data directly from API only."""
    global ctb_routes_cache, ctb_cache_timestamp

    # Fetch from API
    ctb_routes = fetch_ctb_routes_from_api()

    if ctb_routes:
        ctb_routes_cache = ctb_routes
        ctb_cache_timestamp = time.time()
        logger.info(f"Successfully loaded {len(ctb_routes)} CTB routes from API")
        return ctb_routes
    else:
        logger.error("Failed to fetch CTB routes from API")
        ctb_routes_cache = []
        return []

def get_ctb_routes_cached():
    """Get CTB routes with smart caching (refresh every 30 days)."""
    global ctb_routes_cache, ctb_cache_timestamp

    # Check if cache is still valid (30 days = 30 * 24 * 3600 seconds)
    # Routes change very rarely, so longer cache is appropriate
    cache_max_age = 30 * 24 * 3600  # 30 days
    current_time = time.time()

    if (ctb_routes_cache is None or
        ctb_cache_timestamp is None or
        (current_time - ctb_cache_timestamp) > cache_max_age):

        logger.info("CTB cache expired or empty, refreshing...")
        return load_ctb_data_from_api()

    cache_age_days = (current_time - ctb_cache_timestamp) / (24 * 3600)
    logger.info(f"Using cached CTB routes ({len(ctb_routes_cache)} routes, cached {cache_age_days:.1f} days ago)")
    return ctb_routes_cache

def fetch_gmb_routes_from_api():
    """Fetch GMB routes by getting route codes first, then detailed route info."""
    all_routes = []

    try:
        # Step 1: Get all route codes from the main endpoint
        url = "https://data.etagmb.gov.hk/route"
        logger.info(f"Fetching GMB route codes from {url}")

        response = http_session.get(url, timeout=8)  # Faster timeout for Vercel
        if response.status_code == 200:
            routes_data = response.json()

            # Extract route codes by region
            route_codes_by_region = {}

            if isinstance(routes_data, dict) and 'data' in routes_data:
                data = routes_data['data']

                # Check if data has 'routes' key with regional data
                if isinstance(data, dict) and 'routes' in data:
                    routes_by_region = data['routes']

                    # Extract route codes for each region
                    for region in GMB_REGIONS:  # ['HKI', 'KLN', 'NT']
                        if region in routes_by_region:
                            region_routes = routes_by_region[region]

                            if isinstance(region_routes, list):
                                route_codes_by_region[region] = region_routes
                                logger.info(f"Found {len(region_routes)} route codes for region {region}")
                            else:
                                logger.warning(f"Unexpected format for region {region} routes: {type(region_routes)}")
                                route_codes_by_region[region] = []
                        else:
                            logger.warning(f"Region {region} not found in API response")
                            route_codes_by_region[region] = []
                else:
                    logger.warning(f"Unexpected GMB API data structure: {type(data)}")
                    return all_routes
            else:
                logger.warning(f"Unexpected GMB API response format: {type(routes_data)}")
                return all_routes

            # Step 2: Fetch detailed route information for each route code
            for region, route_codes in route_codes_by_region.items():
                logger.info(f"Fetching detailed route info for {len(route_codes)} routes in region {region}")

                for route_code in route_codes:
                    try:
                        detail_url = f"https://data.etagmb.gov.hk/route/{region}/{route_code}"
                        detail_response = http_session.get(detail_url, timeout=6)  # Faster for Vercel

                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()

                            if isinstance(detail_data, dict) and 'data' in detail_data:
                                data_array = detail_data['data']

                                # The API returns data as an array of route objects
                                if isinstance(data_array, list) and len(data_array) > 0:
                                    # Process each route object in the data array
                                    for route_obj in data_array:
                                        if isinstance(route_obj, dict):
                                            # Extract route_id from the route object level
                                            route_id = route_obj.get('route_id')
                                            description_tc = route_obj.get('description_tc')
                                            # Extract directions from the route object
                                            directions = route_obj.get('directions', [])

                                            for direction in directions:
                                                if isinstance(direction, dict):
                                                    # Map route_seq to bound (1=O, 2=I)
                                                    route_seq = direction.get('route_seq', 1)
                                                    bound = 'O' if route_seq == 1 else 'I'

                                                    # Create clean route object with only needed fields
                                                    clean_route = {
                                                        'region': region,
                                                        'co': 'GMB',
                                                        'route': route_code,
                                                        'route_id': route_id,
                                                        'description_tc': description_tc,
                                                        'route_seq': route_seq,
                                                        'bound': bound,
                                                        'dest_en': direction.get('dest_en', ''),
                                                        'dest_sc': direction.get('dest_sc', ''),
                                                        'dest_tc': direction.get('dest_tc', ''),
                                                        'orig_en': direction.get('orig_en', ''),
                                                        'orig_sc': direction.get('orig_sc', ''),
                                                        'orig_tc': direction.get('orig_tc', '')
                                                    }

                                                    all_routes.append(clean_route)

                                            logger.debug(f"Successfully processed route {route_code} in region {region} ({len(directions)} directions)")
                                else:
                                    logger.warning(f"Unexpected data format for route {route_code} in region {region}: {type(data_array)}")
                            else:
                                logger.warning(f"No data field in response for route {route_code} in region {region}")
                        else:
                            logger.warning(f"Failed to fetch details for route {route_code} in region {region}: HTTP {detail_response.status_code}")

                    except Exception as e:
                        try:
                            logger.warning(f"Error fetching details for route {route_code} in region {region}: {e}")
                        except Exception as log_error:
                            print(f"Logger error: {log_error}")
                            print(f"Original error: route_code={route_code}, region={region}, error={e}")
                        continue

            logger.info(f"Successfully fetched detailed info for {len(all_routes)} GMB routes")

        else:
            logger.warning(f"Failed to fetch GMB route codes: HTTP {response.status_code}")

    except Exception as e:
        logger.error(f"Error fetching GMB routes: {e}")

    logger.info(f"Total GMB routes available: {len(all_routes)}")
    return all_routes

def get_gmb_routes_cached():
    """Get GMB routes with caching (refresh every 30 days)."""
    global gmb_routes_cache, gmb_cache_timestamp

    # Check if cache is valid (30 days)
    # GMB routes change very rarely, so longer cache is appropriate
    cache_max_age = 30 * 24 * 3600  # 30 days
    current_time = time.time()

    if (gmb_routes_cache is None or
        gmb_cache_timestamp is None or
        (current_time - gmb_cache_timestamp) > cache_max_age):

        logger.info("GMB cache expired or empty, refreshing...")
        fresh_routes = fetch_gmb_routes_from_api()

        if fresh_routes:
            gmb_routes_cache = fresh_routes
            gmb_cache_timestamp = current_time
            logger.info(f"GMB routes cached successfully: {len(fresh_routes)} routes")
            return fresh_routes
        else:
            logger.error("Failed to fetch GMB routes")
            return gmb_routes_cache if gmb_routes_cache else []

    cache_age_days = (current_time - gmb_cache_timestamp) / (24 * 3600)
    logger.info(f"Using cached GMB routes ({len(gmb_routes_cache)} routes, cached {cache_age_days:.1f} days ago)")
    return gmb_routes_cache

def fetch_json_with_retry(url, max_retries=3, timeout=10):
    """Fetch JSON with retry logic and configurable timeout."""
    for attempt in range(max_retries):
        try:
            response = http_session.get(url, timeout=timeout)
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"HTTP {response.status_code} for URL: {url}")
        except Exception as e:
            logger.warning(f"Error fetching {url} (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(1)  # Reduced sleep time for Vercel

    logger.error(f"Failed to fetch {url} after {max_retries} attempts")
    return None

def fetch_stops_concurrent(stop_ids, api_base, max_workers=10):
    """Fetch multiple stop details concurrently using ThreadPoolExecutor."""
    def fetch_single_stop(stop_id):
        url = f"{api_base}/stop/{stop_id}"
        result = fetch_json_with_retry(url)
        if result and result.get('data'):
            return stop_id, result['data']
        return stop_id, None

    stop_data = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_stop = {executor.submit(fetch_single_stop, stop_id): stop_id
                         for stop_id in stop_ids}

        # Collect results
        for future in as_completed(future_to_stop):
            stop_id, data = future.result()
            if data:
                stop_data[stop_id] = data
            else:
                logger.warning(f"Failed to fetch stop data for {stop_id}")

    return stop_data

@app.route('/')
def index():
    """Serve the main HTML page."""
    try:
        # Try to read index.html from the same directory
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except:
        # Fallback HTML if index.html is not found
        return '''
        <!DOCTYPE html>
        <html>
        <head><title>Bus Route App</title></head>
        <body>
            <h1>Bus Route App</h1>
            <p>API is running. Please ensure index.html is properly deployed.</p>
            <p><a href="/get_bus_routes">Test API</a></p>
        </body>
        </html>
        '''

@app.route('/get_bus_routes')
def get_bus_routes():
    """Get all bus routes with caching and timeout handling."""
    start_time = time.time()

    try:
        # Check cache first
        cache_key = cache_manager._generate_key('all_routes')
        cached_routes = get_cached_routes(cache_key)

        if cached_routes:
            logger.info(f"Routes served from cache in {time.time() - start_time:.3f}s")
            return jsonify(cached_routes)

        # Load KMB + CTB routes (GMB temporarily disabled)
        logger.info("Loading routes for Vercel - KMB + CTB only (GMB temporarily disabled)")

        # Use optimized load approach (GMB disabled in this function for Vercel)
        bus_routes = load_bus_routes_optimized()

        # Cache the result
        if bus_routes:
            cache_routes(cache_key, bus_routes)
            logger.info(f"Routes loaded and cached in {time.time() - start_time:.3f}s")
            return jsonify(bus_routes)
        else:
            logger.warning("No routes loaded, returning empty array")
            return jsonify([])

    except Exception as e:
        logger.error(f"Error in get_bus_routes: {e}")
        return jsonify({"error": "Failed to load routes", "message": str(e)}), 500

def get_gmb_routes_with_kv_cache():
    """
    Get GMB routes using KV cache first, with fallback to empty list.
    This replaces the old get_gmb_routes_cached() function for Vercel.
    """
    try:
        # Try to get GMB routes from KV cache
        logger.info("🔍 Checking KV cache for GMB routes...")
        cached_gmb_routes = kv_cache.get_gmb_routes()

        if cached_gmb_routes:
            # Cache hit - return cached routes
            logger.info(f"✅ GMB routes loaded from KV cache - {len(cached_gmb_routes)} routes available")
            return cached_gmb_routes
        else:
            # Cache miss - fallback to KMB+CTB only
            logger.info("❌ GMB routes cache miss - falling back to KMB+CTB only mode")
            logger.info("💡 To enable GMB routes, run the monthly refresh job: /cron/refresh-gmb")
            return []

    except Exception as e:
        logger.error(f"❌ Error accessing GMB routes from KV cache: {e}")
        logger.info("🔄 Falling back to KMB+CTB only mode due to error")
        return []

def load_bus_routes_optimized():
    """Load bus routes with caching - KMB, CTB, and GMB from APIs."""
    try:
        # Fetch KMB routes
        logger.info("Fetching KMB routes...")
        url = "https://data.etabus.gov.hk/v1/transport/kmb/route/"
        kmb_result = fetch_json_with_retry(url)

        if kmb_result and kmb_result.get('data'):
            kmb_routes_data = kmb_result['data']
            # Add company identifier
            for route in kmb_routes_data:
                route["co"] = "KMB"
            logger.info(f"Successfully fetched {len(kmb_routes_data)} KMB routes")
        else:
            logger.error("Error loading KMB routes data")
            kmb_routes_data = []

        # Fetch CTB routes from API (with caching)
        ctb_routes_data = get_ctb_routes_cached()

        # Fetch GMB routes from KV cache (with fallback)
        gmb_routes_data = get_gmb_routes_with_kv_cache()

        # Combine data
        combined_data = kmb_routes_data + ctb_routes_data + gmb_routes_data
        logger.info(f"Combined routes: {len(kmb_routes_data)} KMB + {len(ctb_routes_data)} CTB + {len(gmb_routes_data)} GMB = {len(combined_data)} total")
        return combined_data

    except Exception as e:
        logger.error(f"Error in load_bus_routes_optimized: {e}")
        return []

@app.route('/get_kmb_bus_routestop')
def get_kmb_bus_routestop():
    """Get KMB route stops with caching and concurrent optimization."""
    start_time = time.time()

    # Extract parameters
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    service_type = request.args.get('s')
    user_lat = request.args.get('lat')
    user_lon = request.args.get('lon')

    # Generate cache key
    cache_key = cache_manager._generate_key(
        'kmb_routestop', route_param, bound_param, service_type, user_lat, user_lon
    )

    # Check cache
    cached_result = get_cached_routestops(cache_key)
    if cached_result:
        logger.info(f"KMB route stops served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_result)

    # Load fresh data
    bus_routestop = load_kmb_bus_routestop_optimized(
        route_param, bound_param, service_type, user_lat, user_lon
    )

    # Cache result
    if bus_routestop:
        cache_routestops(cache_key, bus_routestop)

    logger.info(f"KMB route stops loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_routestop if bus_routestop is not None else [])

def load_kmb_bus_routestop_optimized(route_param, bound_param, service_type, user_lat, user_lon):
    """Load KMB route stops with concurrent API calls."""
    global route, bound
    route = route_param
    bound = bound_param

    boundtype = 'inbound' if bound_param == 'I' else 'outbound'

    try:
        # Fetch route stops
        url = f"https://data.etabus.gov.hk/v1/transport/kmb/route-stop/{route_param}/{boundtype}/{service_type}"
        routestop_result = fetch_json_with_retry(url)

        if not routestop_result or not routestop_result.get('data'):
            logger.error("Error loading KMB bus routestop data")
            return None

        routestop_data = routestop_result['data']

        # Extract stop IDs
        stop_ids = [row["stop"] for row in routestop_data]

        # Fetch all stop details concurrently
        stop_details = fetch_stops_concurrent(stop_ids, "https://data.etabus.gov.hk/v1/transport/kmb")

        # Build result list
        stop_list = []
        for row in routestop_data:
            stop_id = row["stop"]
            stop_seq = row["seq"]

            if stop_id in stop_details:
                stop_data = stop_details[stop_id]
                km = ''
                if user_lat and user_lon:
                    km = haversine(
                        float(user_lat), float(user_lon),
                        float(stop_data["lat"]), float(stop_data["long"])
                    )

                # Clean the stop name to remove stop ID codes
                cleaned_stop_name = clean_stop_name(stop_data["name_tc"])

                stop_obj = [stop_id, cleaned_stop_name, stop_seq, km]
                stop_list.append(stop_obj)
            else:
                logger.warning(f"Missing stop data for {stop_id}")

        return stop_list

    except Exception as e:
        logger.error(f"Error in load_kmb_bus_routestop_optimized: {e}")
        return None

@app.route('/get_kmb_stop_eta')
def get_kmb_stop_eta():
    """Get KMB stop ETA with optional cache bypass for real-time updates."""
    start_time = time.time()

    # Extract parameters
    stop_id = request.args.get('id')
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    force_refresh = request.args.get('force_refresh', '').lower() == 'true'

    # Generate cache key
    cache_key = cache_manager._generate_key('kmb_eta', stop_id, route_param, bound_param)

    # Check cache only if not forcing refresh
    if not force_refresh:
        cached_eta = get_cached_eta(cache_key)
        if cached_eta:
            logger.info(f"KMB ETA served from cache in {time.time() - start_time:.3f}s")
            return jsonify(cached_eta)
    else:
        logger.info(f"KMB ETA cache bypassed (force_refresh=true)")

    # Load fresh ETA data
    bus_stop_eta = load_kmb_stop_eta_optimized(stop_id, route_param, bound_param)

    # Cache result only if not forcing refresh (to allow immediate subsequent requests to use cache)
    if bus_stop_eta and not force_refresh:
        cache_eta(cache_key, bus_stop_eta)
    elif force_refresh:
        logger.info(f"KMB ETA not cached due to force_refresh")

    logger.info(f"KMB ETA loaded {'(FRESH)' if force_refresh else ''} in {time.time() - start_time:.3f}s")
    return jsonify(bus_stop_eta if bus_stop_eta is not None else [])

def load_kmb_stop_eta_optimized(stop_id, route_param, bound_param):
    """Load KMB stop ETA with optimization."""
    global route, bound
    route = route_param
    bound = bound_param

    try:
        url = f"https://data.etabus.gov.hk/v1/transport/kmb/stop-eta/{stop_id}"
        eta_result = fetch_json_with_retry(url)

        if not eta_result or not eta_result.get('data'):
            logger.error("Error loading KMB bus stop eta data")
            return None

        eta_list = []
        for row in eta_result['data']:
            if (row["route"] == route_param and
                row["dir"] == bound_param and
                row["service_type"] == 1):

                time_eta = ''
                eta = ''
                rmk_tc = row["rmk_tc"]
                time_current = datetime.strptime(row["data_timestamp"], '%Y-%m-%dT%H:%M:%S+08:00')

                if row["eta"] and row["eta"] != '':
                    time_eta = datetime.strptime(row["eta"], '%Y-%m-%dT%H:%M:%S+08:00')
                    delta = time_eta - time_current
                    minutes, seconds = divmod(delta.total_seconds(), 60)
                    eta = f"{int(minutes)} minutes {int(seconds)} seconds"
                    rmk_tc = '九巴'
                    if delta.total_seconds() < 0:
                        rmk_tc = '已到達'

                dt_current_str = time_current.strftime("%Y-%m-%d %H:%M:%S")
                eta_obj = [time_eta, eta, rmk_tc, dt_current_str]
                eta_list.append(eta_obj)

        return eta_list

    except Exception as e:
        logger.error(f"Error in load_kmb_stop_eta_optimized: {e}")
        return None

@app.route('/get_ctb_bus_routestop')
def get_ctb_bus_routestop():
    """Get CTB route stops with caching and concurrent optimization."""
    start_time = time.time()

    # Extract parameters
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    user_lat = request.args.get('lat')
    user_lon = request.args.get('lon')

    # Generate cache key
    cache_key = cache_manager._generate_key(
        'ctb_routestop', route_param, bound_param, user_lat, user_lon
    )

    # Check cache
    cached_result = get_cached_routestops(cache_key)
    if cached_result:
        logger.info(f"CTB route stops served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_result)

    # Load fresh data
    bus_routestop = load_ctb_bus_routestop_optimized(
        route_param, bound_param, user_lat, user_lon
    )

    # Cache result
    if bus_routestop:
        cache_routestops(cache_key, bus_routestop)

    logger.info(f"CTB route stops loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_routestop if bus_routestop is not None else [])

def load_ctb_bus_routestop_optimized(route_param, bound_param, user_lat, user_lon):
    """Load CTB route stops with concurrent API calls."""
    global route, bound
    route = route_param
    bound = bound_param

    boundtype = 'inbound' if bound_param == 'I' else 'outbound'

    try:
        # Fetch route stops
        url = f"https://rt.data.gov.hk/v2/transport/citybus/route-stop/CTB/{route_param}/{boundtype}"
        routestop_result = fetch_json_with_retry(url)

        if not routestop_result or not routestop_result.get('data'):
            logger.error("Error loading CTB bus routestop data")
            return None

        routestop_data = routestop_result['data']

        # Extract stop IDs
        stop_ids = [row["stop"] for row in routestop_data]

        # Fetch all stop details concurrently
        stop_details = fetch_stops_concurrent(stop_ids, "https://rt.data.gov.hk/v2/transport/citybus")

        # Build result list
        stop_list = []
        for row in routestop_data:
            stop_id = row["stop"]
            stop_seq = row["seq"]

            if stop_id in stop_details:
                stop_data = stop_details[stop_id]
                km = ''
                if user_lat and user_lon:
                    km = haversine(
                        float(user_lat), float(user_lon),
                        float(stop_data["lat"]), float(stop_data["long"])
                    )

                # Clean the stop name to remove stop ID codes (for consistency)
                cleaned_stop_name = clean_stop_name(stop_data["name_tc"])

                stop_obj = [stop_id, cleaned_stop_name, stop_seq, km]
                stop_list.append(stop_obj)
            else:
                logger.warning(f"Missing CTB stop data for {stop_id}")

        return stop_list

    except Exception as e:
        logger.error(f"Error in load_ctb_bus_routestop_optimized: {e}")
        return None

@app.route('/get_ctb_stop_eta')
def get_ctb_stop_eta():
    """Get CTB stop ETA with optional cache bypass for real-time updates."""
    start_time = time.time()

    # Extract parameters
    stop_id = request.args.get('id')
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    force_refresh = request.args.get('force_refresh', '').lower() == 'true'

    # Generate cache key
    cache_key = cache_manager._generate_key('ctb_eta', stop_id, route_param, bound_param)

    # Check cache only if not forcing refresh
    if not force_refresh:
        cached_eta = get_cached_eta(cache_key)
        if cached_eta:
            logger.info(f"CTB ETA served from cache in {time.time() - start_time:.3f}s")
            return jsonify(cached_eta)
    else:
        logger.info(f"CTB ETA cache bypassed (force_refresh=true)")

    # Load fresh ETA data
    bus_stop_eta = load_ctb_stop_eta_optimized(stop_id, route_param, bound_param)

    # Cache result only if not forcing refresh
    if bus_stop_eta and not force_refresh:
        cache_eta(cache_key, bus_stop_eta)
    elif force_refresh:
        logger.info(f"CTB ETA not cached due to force_refresh")

    logger.info(f"CTB ETA loaded {'(FRESH)' if force_refresh else ''} in {time.time() - start_time:.3f}s")
    return jsonify(bus_stop_eta if bus_stop_eta is not None else [])

def load_ctb_stop_eta_optimized(stop_id, route_param, bound_param):
    """Load CTB stop ETA with optimization."""
    try:
        url = f"https://rt.data.gov.hk/v2/transport/citybus/eta/CTB/{stop_id}/{route_param}"
        eta_result = fetch_json_with_retry(url)

        if not eta_result or not eta_result.get('data'):
            logger.error("Error loading CTB bus stop eta data")
            return None

        eta_list = []
        for row in eta_result['data']:
            if (row["route"] == route_param and row["dir"] == bound_param):
                time_eta = ''
                eta = ''
                rmk_tc = '城巴'
                time_current = datetime.strptime(row["data_timestamp"], '%Y-%m-%dT%H:%M:%S+08:00')

                if row["eta"] and row["eta"] != '':
                    time_eta = datetime.strptime(row["eta"], '%Y-%m-%dT%H:%M:%S+08:00')
                    delta = time_eta - time_current
                    minutes, seconds = divmod(delta.total_seconds(), 60)
                    eta = f"{int(minutes)} minutes {int(seconds)} seconds"
                    if delta.total_seconds() < 0:
                        rmk_tc = "已到達"

                dt_current_str = time_current.strftime("%Y-%m-%d %H:%M:%S")
                eta_obj = [time_eta, eta, rmk_tc, dt_current_str]
                eta_list.append(eta_obj)

        return eta_list

    except Exception as e:
        logger.error(f"Error in load_ctb_stop_eta_optimized: {e}")
        return None

@app.route('/get_share_bus_stop')
def get_share_bus_stop():
    """Get shared bus stop with caching."""
    start_time = time.time()

    # Extract parameters
    seq = request.args.get('s')
    route_param = request.args.get('r')
    bound_param = request.args.get('b')

    # Generate cache key
    cache_key = cache_manager._generate_key('share_stop', seq, route_param, bound_param)

    # Check cache
    cached_result = get_cached_stops(cache_key)
    if cached_result:
        logger.info(f"Share bus stop served from cache in {time.time() - start_time:.3f}s")
        return cached_result if cached_result else ''

    # Load fresh data
    bus_stop = load_share_bus_stop_optimized(seq, route_param, bound_param)

    # Cache result
    if bus_stop is not None:
        cache_stops(cache_key, bus_stop)

    logger.info(f"Share bus stop loaded in {time.time() - start_time:.3f}s")
    return bus_stop if bus_stop is not None else ''

def load_share_bus_stop_optimized(seq, route_param, bound_param):
    """Load shared bus stop with optimization."""
    global route, bound
    route = route_param
    bound = bound_param

    boundtype = 'inbound' if bound_param == 'I' else 'outbound'

    try:
        dest_en = check_kmb_route_optimized(route_param, boundtype)
        if dest_en is not None:
            boundtype = check_ctb_route_optimized(dest_en, route_param)

        if boundtype is not None:
            url = f"https://rt.data.gov.hk/v2/transport/citybus/route-stop/CTB/{route_param}/{boundtype}"
            routestop_result = fetch_json_with_retry(url)

            if routestop_result and routestop_result.get('data'):
                for row in routestop_result['data']:
                    if str(row["seq"]) == seq:
                        return row["stop"]
            else:
                logger.error("Error loading CTB bus routestop data")
                return None
        else:
            return None

    except Exception as e:
        logger.error(f"Error in load_share_bus_stop_optimized: {e}")
        return None

def check_kmb_route_optimized(route_param, boundtype):
    """Check KMB route with optimization."""
    try:
        url = f"https://data.etabus.gov.hk/v1/transport/kmb/route/{route_param}/{boundtype}/1"
        result = fetch_json_with_retry(url)

        if result and result.get('data') and isinstance(result['data'], dict):
            return result['data'].get('dest_en')
        else:
            logger.warning("Invalid KMB route response format")
            return None

    except Exception as e:
        logger.error(f"Error checking KMB route: {e}")
        return None

def check_ctb_route_optimized(dest_en, route_param):
    """Check CTB route with optimization."""
    try:
        url = f"https://rt.data.gov.hk/v2/transport/citybus/route/CTB/{route_param}"
        result = fetch_json_with_retry(url)

        if result and result.get('data') and isinstance(result['data'], dict):
            if not result['data']:
                return None

            dest_en_cmb = result['data'].get('dest_en')
            if dest_en_cmb and (dest_en in dest_en_cmb or dest_en_cmb in dest_en):
                return "outbound"
            else:
                return "inbound"
        else:
            logger.warning("Invalid CTB route response format")
            return None

    except Exception as e:
        logger.error(f"Error checking CTB route: {e}")
        return None

@app.route('/get_gmb_bus_routestop')
def get_gmb_bus_routestop():
    """Get GMB route stops with caching."""
    start_time = time.time()

    # Extract parameters
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    user_lat = request.args.get('lat')
    user_lon = request.args.get('lon')

    # Generate cache key
    cache_key = cache_manager._generate_key(
        'gmb_routestop', route_param, bound_param, user_lat, user_lon
    )

    # Check cache
    cached_result = get_cached_routestops(cache_key)
    if cached_result:
        logger.info(f"GMB route stops served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_result)

    # Load fresh data
    bus_routestop = load_gmb_bus_routestop_optimized(
        route_param, bound_param, user_lat, user_lon
    )

    # Cache result
    if bus_routestop:
        cache_routestops(cache_key, bus_routestop)

    logger.info(f"GMB route stops loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_routestop if bus_routestop is not None else [])

def load_gmb_bus_routestop_optimized(route_param, bound_param, user_lat, user_lon):
    """Load GMB route stops from API using route-stop endpoint."""
    route_seq = bound_param

    try:
        # Fetch route stops
        route_url = f"https://data.etagmb.gov.hk/route-stop/{route_param}/{route_seq}"
        route_result = fetch_json_with_retry(route_url)

        if not route_result or not route_result.get('data'):
            logger.warning(f"Error loading GMB route stops for route_id {route_param}, route_seq {route_seq}")
            return []

        stops_data = route_result['data']

        # Handle different response formats
        if isinstance(stops_data, list):
            stops = stops_data
        elif isinstance(stops_data, dict) and 'route_stops' in stops_data:
            stops = stops_data['route_stops']
        else:
            logger.warning(f"Unexpected stops data format for route_id {route_param}")
            return []

        if not stops:
            logger.warning(f"No stops found for GMB route_id {route_param}, route_seq {route_seq}")
            return []

        # Build result list
        stop_list = []
        for i, stop in enumerate(stops):
            if isinstance(stop, dict):
                stop_id = stop.get('stop_id')
                stop_name = stop.get('name_tc', stop.get('name_en', ''))
                stop_seq = stop.get('stop_seq', i + 1)
                # Calculate distance if user location provided
                km = ''
                if user_lat and user_lon:
                    try:
                        # Use the stop endpoint with stop_id to get the latitude and longitude in wgs84
                        stop_url = f"https://data.etagmb.gov.hk/stop/{stop_id}"

                        stop_detail = fetch_json_with_retry(stop_url)

                        if stop_detail and 'data' in stop_detail:
                            stop_data = stop_detail['data']
                            coordinates = stop_data.get('coordinates', {})
                            wgs84 = coordinates.get('wgs84', {})
                            stop_lat = wgs84.get('latitude')
                            stop_long = wgs84.get('longitude')

                            if stop_lat and stop_long:
                                km = haversine(
                                    float(user_lat), float(user_lon),
                                    float(stop_lat), float(stop_long)
                                )
                            else:
                                logger.warning(f"No WGS84 coordinates found for GMB stop {stop_id}")
                        else:
                            logger.warning(f"Failed to fetch stop details for GMB stop {stop_id}")

                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error calculating distance for GMB stop {stop_id}: {e}")
                        km = ''
                    except Exception as e:
                        logger.error(f"Unexpected error fetching GMB stop coordinates for {stop_id}: {e}")
                        km = ''

                stop_obj = [stop_id, stop_name, stop_seq, km]  # seq is index + 1
                stop_list.append(stop_obj)

        return stop_list

    except Exception as e:
        logger.error(f"Error in load_gmb_bus_routestop_optimized: {e}")
        return []

@app.route('/get_gmb_stop_eta')
def get_gmb_stop_eta():
    """Get GMB stop ETA with caching."""
    start_time = time.time()

    # Extract parameters
    stop_id = request.args.get('id')
    route_param = request.args.get('r')
    stop_seq = request.args.get('s')
    force_refresh = request.args.get('force_refresh', '').lower() == 'true'

    # Generate cache key
    cache_key = cache_manager._generate_key('gmb_eta', stop_id, route_param, stop_seq)

    # Check cache only if not forcing refresh
    if not force_refresh:
        cached_eta = get_cached_eta(cache_key)
        if cached_eta:
            logger.info(f"GMB ETA served from cache in {time.time() - start_time:.3f}s")
            return jsonify(cached_eta)
    else:
        logger.info(f"GMB ETA cache bypassed (force_refresh=true)")

    # Load fresh ETA data
    bus_stop_eta = load_gmb_stop_eta_optimized(stop_id, route_param, stop_seq)

    # Cache result only if not forcing refresh
    if bus_stop_eta and not force_refresh:
        cache_eta(cache_key, bus_stop_eta)
    elif force_refresh:
        logger.info(f"GMB ETA not cached due to force_refresh")

    logger.info(f"GMB ETA loaded {'(FRESH)' if force_refresh else ''} in {time.time() - start_time:.3f}s")
    return jsonify(bus_stop_eta if bus_stop_eta is not None else [])

def load_gmb_stop_eta_optimized(stop_id, route_param, stop_seq):
    """Load GMB stop ETA from API."""
    try:
        # Use route_param as route_id directly for ETA endpoint
        route_id = route_param

        # Get ETA data using route-stop endpoint
        eta_url = f"https://data.etagmb.gov.hk/eta/route-stop/{route_id}/{stop_id}"
        eta_result = fetch_json_with_retry(eta_url)

        if not eta_result or not eta_result.get('data'):
            logger.warning(f"No GMB ETA data for route {route_param} stop {stop_id}")
            return []

        eta_list = []

        for data_item in eta_result['data']:
            if data_item.get('stop_seq') == int(stop_seq):
                eta_entries = data_item.get('eta', [])

                for eta_entry in eta_entries:
                    time_eta = ''
                    eta = ''
                    rmk_tc = eta_entry.get('remarks_tc') or '小巴'
                    if rmk_tc == '未開出':
                        rmk_tc = '小巴'

                    # Handle timestamps with milliseconds
                    generated_timestamp = eta_result["generated_timestamp"]
                if '.' in generated_timestamp:
                    time_current = datetime.strptime(generated_timestamp, '%Y-%m-%dT%H:%M:%S.%f+08:00')
                else:
                    time_current = datetime.strptime(generated_timestamp, '%Y-%m-%dT%H:%M:%S+08:00')

                if eta_entry.get("timestamp"):
                    eta_timestamp = eta_entry["timestamp"]
                    if '.' in eta_timestamp:
                        time_eta = datetime.strptime(eta_timestamp, '%Y-%m-%dT%H:%M:%S.%f+08:00')
                    else:
                        time_eta = datetime.strptime(eta_timestamp, '%Y-%m-%dT%H:%M:%S+08:00')
                    delta = time_eta - time_current
                    minutes, seconds = divmod(delta.total_seconds(), 60)
                    eta = f"{int(minutes)} minutes {int(seconds)} seconds"

                    if delta.total_seconds() < 0:
                        rmk_tc = "已到達"

                    dt_current_str = time_current.strftime("%Y-%m-%d %H:%M:%S")
                    eta_obj = [time_eta, eta, rmk_tc, dt_current_str]
                    eta_list.append(eta_obj)
                    
        return eta_list

    except Exception as e:
        logger.error(f"Error in load_gmb_stop_eta_optimized: {e}")
        return None

def clean_stop_name(stop_name):
    """Clean KMB stop names by removing stop ID codes while keeping platform indicators."""
    if not stop_name:
        return stop_name

    import re

    # Pattern to match stop ID codes: 2+ letters followed by 2+ numbers in parentheses
    # This excludes single letter platform indicators like (A2), (B1), (T1)
    # Examples: (TA754), (HO06), (KA123), (TST67), (AP001), etc.
    stop_id_pattern = r'\s*\([A-Z]{2,}\d{2,}\)'

    # Remove stop ID codes
    cleaned_name = re.sub(stop_id_pattern, '', stop_name)

    # Clean up any extra spaces
    cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()

    return cleaned_name

def haversine(lat1, lon1, lat2, lon2):
    """Calculate the great circle distance between two points on earth."""
    R = 6371  # Earth radius in km
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    return R * c

@app.route('/cache_stats')
def cache_stats():
    """Get cache statistics for monitoring."""
    stats = cache_manager.get_cache_stats()

    # Add CTB cache info
    global ctb_cache_timestamp, ctb_routes_cache
    if ctb_cache_timestamp:
        cache_age = time.time() - ctb_cache_timestamp
        stats['ctb_cache_age_days'] = round(cache_age / (24 * 3600), 2)
        stats['ctb_cache_age_hours'] = round(cache_age / 3600, 2)
        stats['ctb_routes_count'] = len(ctb_routes_cache) if ctb_routes_cache else 0
        stats['ctb_cache_valid_for_days'] = round((30 * 24 * 3600 - cache_age) / (24 * 3600), 2)
    else:
        stats['ctb_cache_age_days'] = 'Not loaded'
        stats['ctb_cache_age_hours'] = 'Not loaded'
        stats['ctb_routes_count'] = 0
        stats['ctb_cache_valid_for_days'] = 'N/A'

    # Add GMB cache info
    global gmb_cache_timestamp, gmb_routes_cache
    if gmb_cache_timestamp:
        cache_age = time.time() - gmb_cache_timestamp
        stats['gmb_cache_age_days'] = round(cache_age / (24 * 3600), 2)
        stats['gmb_cache_age_hours'] = round(cache_age / 3600, 2)
        stats['gmb_routes_count'] = len(gmb_routes_cache) if gmb_routes_cache else 0
        stats['gmb_cache_valid_for_days'] = round((30 * 24 * 3600 - cache_age) / (24 * 3600), 2)
    else:
        stats['gmb_cache_age_days'] = 'Not loaded'
        stats['gmb_cache_age_hours'] = 'Not loaded'
        stats['gmb_routes_count'] = 0
        stats['gmb_cache_valid_for_days'] = 'N/A'

    return jsonify(stats)

@app.route('/health')
def health():
    """Health check endpoint for Vercel."""
    return jsonify({
        'status': 'healthy',
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'version': 'optimized'
    })

@app.route('/warmup')
def warmup():
    """Warmup endpoint to initialize caches."""
    try:
        start_time = time.time()

        # Initialize CTB cache in background
        logger.info("Warming up CTB cache...")
        ctb_routes = get_ctb_routes_cached()

        # Check GMB routes from KV cache (no API calls during warmup)
        logger.info("Checking GMB routes from KV cache...")
        gmb_routes = kv_cache.get_gmb_routes()
        if gmb_routes:
            logger.info(f"GMB routes available from KV cache: {len(gmb_routes)} routes")
        else:
            logger.info("GMB routes not available in KV cache - will fallback to KMB+CTB only")
            gmb_routes = []

        elapsed = time.time() - start_time
        return jsonify({
            'status': 'warmed_up',
            'elapsed_seconds': round(elapsed, 2),
            'ctb_routes_count': len(ctb_routes) if ctb_routes else 0,
            'gmb_routes_count': len(gmb_routes) if gmb_routes else 0,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        logger.error(f"Warmup error: {e}")
        return jsonify({
            'status': 'warmup_failed',
            'error': str(e),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/warmup_background')
def warmup_background():
    """Background warmup that doesn't block - for triggering after initial load."""
    try:
        # This endpoint returns immediately but triggers background loading
        import threading

        def background_warmup():
            try:
                logger.info("Background warmup starting...")
                get_ctb_routes_cached()
                # Check GMB routes from KV cache (no API calls)
                gmb_routes = kv_cache.get_gmb_routes()
                gmb_status = f"{len(gmb_routes)} routes" if gmb_routes else "not available"
                logger.info(f"Background warmup completed - GMB from KV cache: {gmb_status}")
            except Exception as e:
                logger.error(f"Background warmup error: {e}")

        # Start background thread
        thread = threading.Thread(target=background_warmup)
        thread.daemon = True
        thread.start()

        return jsonify({
            'status': 'background_warmup_started',
            'message': 'CTB routes loading in background, GMB from KV cache if available',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        logger.error(f"Background warmup error: {e}")
        return jsonify({
            'status': 'background_warmup_failed',
            'error': str(e),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }), 500

# Removed /quick_routes endpoint - KMB-only fallback is not acceptable

@app.route('/cache/clear')
def clear_cache():
    """Clear all caches - useful for forcing refresh of route data."""
    try:
        global ctb_routes_cache, ctb_cache_timestamp, gmb_routes_cache, gmb_cache_timestamp

        # Clear global caches
        ctb_routes_cache = None
        ctb_cache_timestamp = None
        gmb_routes_cache = None
        gmb_cache_timestamp = None

        # Clear TTL caches
        for cache_name, cache in cache_manager.caches.items():
            cache.clear()

        # Clear KV cache for GMB routes
        kv_cleared = kv_cache.clear_gmb_cache()
        kv_status = "cleared" if kv_cleared else "not available"

        logger.info(f"All caches cleared manually - KV cache: {kv_status}")
        return jsonify({
            'status': 'caches_cleared',
            'message': 'All caches have been cleared. Next request will fetch fresh data.',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        return jsonify({
            'status': 'cache_clear_failed',
            'error': str(e),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/cron/refresh-gmb')
def cron_refresh_gmb():
    """
    Monthly cron job endpoint to refresh GMB routes in KV cache.
    This endpoint should be called by Vercel Cron Jobs monthly.
    """
    try:
        start_time = time.time()
        logger.info("🔄 Monthly GMB cache refresh started...")

        # Check if KV is available
        if not kv_cache.is_available():
            logger.error("❌ KV cache not available - cannot refresh GMB routes")
            return jsonify({
                'status': 'failed',
                'error': 'KV cache not available',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }), 500

        # Fetch fresh GMB routes from API
        logger.info("📡 Fetching fresh GMB routes from API...")
        fresh_gmb_routes = fetch_gmb_routes_from_api()

        if fresh_gmb_routes:
            # Store in KV cache
            success = kv_cache.set_gmb_routes(fresh_gmb_routes)

            if success:
                elapsed = time.time() - start_time
                logger.info(f"✅ Monthly GMB cache refresh completed successfully in {elapsed:.2f}s")
                return jsonify({
                    'status': 'success',
                    'routes_count': len(fresh_gmb_routes),
                    'elapsed_seconds': round(elapsed, 2),
                    'message': 'GMB routes refreshed in KV cache',
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                })
            else:
                logger.error("❌ Failed to store GMB routes in KV cache")
                return jsonify({
                    'status': 'failed',
                    'error': 'Failed to store routes in KV cache',
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }), 500
        else:
            logger.error("❌ Failed to fetch GMB routes from API")
            return jsonify({
                'status': 'failed',
                'error': 'Failed to fetch GMB routes from API',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }), 500

    except Exception as e:
        logger.error(f"❌ Monthly GMB cache refresh error: {e}")
        return jsonify({
            'status': 'failed',
            'error': str(e),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/cache/status')
def cache_status():
    """Get detailed cache status including KV cache information."""
    try:
        # Get KV cache info
        kv_info = kv_cache.get_cache_info()

        # Get memory cache stats
        memory_cache_stats = cache_manager.get_cache_stats()

        # Combine all cache information
        status = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'kv_cache': kv_info,
            'memory_cache': memory_cache_stats,
            'global_cache_status': {
                'ctb_cache_active': ctb_routes_cache is not None,
                'gmb_cache_active': gmb_routes_cache is not None,
                'ctb_cache_age_seconds': (time.time() - ctb_cache_timestamp) if ctb_cache_timestamp else None,
                'gmb_cache_age_seconds': (time.time() - gmb_cache_timestamp) if gmb_cache_timestamp else None
            }
        }

        logger.info("📊 Cache status requested")
        return jsonify(status)

    except Exception as e:
        logger.error(f"❌ Error getting cache status: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }), 500

# Note: Removed automatic initialization to prevent Vercel timeout
# Data will be loaded on-demand when first requested
logger.info("Flask app initialized for Vercel - data will load on-demand")

# Vercel expects the app to be available as 'app'
if __name__ == '__main__':
    app.run(debug=False)
