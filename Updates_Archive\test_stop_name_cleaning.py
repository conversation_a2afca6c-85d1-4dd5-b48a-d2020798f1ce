#!/usr/bin/env python3
"""
Test script for the stop name cleaning function.
Tests various KMB stop name formats to ensure proper cleaning.
"""

import re

def clean_stop_name(stop_name):
    """
    Clean KMB stop names by removing stop ID codes while keeping platform indicators.

    Examples:
    - "青沙公路轉車站 (A2) (TA754)" → "青沙公路轉車站 (A2)"
    - "青沙公路轉車站 (TA754)" → "青沙公路轉車站"
    - "青沙公路轉車站 (A2)" → "青沙公路轉車站 (A2)" (unchanged)
    - "青沙公路轉車站" → "青沙公路轉車站" (unchanged)
    """
    if not stop_name:
        return stop_name

    # Pattern to match stop ID codes: 2+ letters followed by 2+ numbers in parentheses
    # This excludes single letter platform indicators like (A2), (B1), (T1)
    # Examples: (TA754), (HO06), (KA123), (TST67), (AP001), etc.
    stop_id_pattern = r'\s*\([A-Z]{2,}\d{2,}\)'

    # Remove stop ID codes
    cleaned_name = re.sub(stop_id_pattern, '', stop_name)

    # Clean up any extra spaces
    cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()

    return cleaned_name

def test_stop_name_cleaning():
    """Test the stop name cleaning function with various examples."""
    print("🧪 Testing Stop Name Cleaning Function")
    print("=" * 60)

    # Test cases: (input, expected_output, description)
    test_cases = [
        # Main examples from user request
        ("青沙公路轉車站 (A2) (TA754)", "青沙公路轉車站 (A2)", "Remove stop ID, keep platform"),
        ("青沙公路轉車站 (TA754)", "青沙公路轉車站", "Remove stop ID only"),

        # Edge cases
        ("青沙公路轉車站 (A2)", "青沙公路轉車站 (A2)", "Keep platform indicator"),
        ("青沙公路轉車站", "青沙公路轉車站", "No changes needed"),

        # Multiple platform indicators
        ("中環站 (A1) (B2) (HO06)", "中環站 (A1) (B2)", "Keep multiple platforms, remove stop ID"),
        ("中環站 (HO06) (A1)", "中環站 (A1)", "Remove stop ID, keep platform"),

        # Different stop ID formats
        ("銅鑼灣站 (KA123)", "銅鑼灣站", "Remove KA-format stop ID"),
        ("旺角站 (MO45)", "旺角站", "Remove MO-format stop ID"),
        ("尖沙咀站 (TST67)", "尖沙咀站", "Remove TST-format stop ID"),

        # Complex cases
        ("機場客運大樓 (T1) (T2) (AP001)", "機場客運大樓 (T1) (T2)", "Multiple terminals, remove stop ID"),
        ("海洋公園站 (OP789) (南)", "海洋公園站 (南)", "Remove stop ID, keep direction"),

        # English names (should work the same)
        ("Airport Terminal (T1) (AP001)", "Airport Terminal (T1)", "English name with terminal"),
        ("Central Station (HO06)", "Central Station", "English name with stop ID"),

        # Edge cases
        ("", "", "Empty string"),
        (None, None, "None input"),
        ("站名 ()", "站名 ()", "Empty parentheses"),
        ("站名 (123)", "站名 (123)", "Numbers only - keep"),
        ("站名 (ABC)", "站名 (ABC)", "Letters only - keep"),

        # Real-world examples
        ("天水圍站 (B1) (TIS01)", "天水圍站 (B1)", "Real example 1"),
        ("屯門市中心 (TMC02)", "屯門市中心", "Real example 2"),
        ("沙田站 (A) (SHA03)", "沙田站 (A)", "Real example 3"),
    ]

    passed = 0
    failed = 0

    for i, (input_name, expected, description) in enumerate(test_cases, 1):
        try:
            result = clean_stop_name(input_name)

            if result == expected:
                status = "✅ PASS"
                passed += 1
            else:
                status = "❌ FAIL"
                failed += 1

            print(f"{i:2d}. {status} {description}")
            print(f"    Input:    '{input_name}'")
            print(f"    Expected: '{expected}'")
            print(f"    Got:      '{result}'")

            if result != expected:
                print(f"    ❌ MISMATCH!")

            print()

        except Exception as e:
            print(f"{i:2d}. ❌ ERROR {description}")
            print(f"    Input: '{input_name}'")
            print(f"    Error: {e}")
            print()
            failed += 1

    # Summary
    total = passed + failed
    print("=" * 60)
    print(f"📊 Test Results Summary:")
    print(f"   Total tests: {total}")
    print(f"   Passed: {passed} ✅")
    print(f"   Failed: {failed} ❌")
    print(f"   Success rate: {(passed/total*100):.1f}%" if total > 0 else "N/A")

    if failed == 0:
        print("\n🎉 All tests passed! The function is working correctly.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the function logic.")

    return failed == 0

def test_regex_patterns():
    """Test the regex patterns used in the function."""
    print("\n🔍 Testing Regex Patterns")
    print("=" * 40)

    # Test the stop ID pattern
    stop_id_pattern = r'\s*\([A-Z]{2,}\d{2,}\)'

    test_strings = [
        ("(TA754)", True, "Basic stop ID"),
        ("(HO06)", True, "Short stop ID"),
        ("(TST123)", True, "Three-letter stop ID"),
        ("(A2)", False, "Platform indicator"),
        ("(B1)", False, "Platform indicator"),
        ("(T1)", False, "Terminal indicator"),
        ("(123)", False, "Numbers only"),
        ("(ABC)", False, "Letters only"),
        ("(南)", False, "Chinese character"),
        (" (TA754)", True, "With leading space"),
        ("(TA754) ", False, "With trailing space (pattern doesn't match)"),
    ]

    print("Testing stop ID pattern:", stop_id_pattern)
    print()

    for test_str, should_match, description in test_strings:
        import re
        match = re.search(stop_id_pattern, test_str)
        is_match = match is not None

        if is_match == should_match:
            status = "✅"
        else:
            status = "❌"

        print(f"{status} '{test_str}' - {description}")
        print(f"   Expected: {'Match' if should_match else 'No match'}")
        print(f"   Got: {'Match' if is_match else 'No match'}")
        if match:
            print(f"   Matched: '{match.group()}'")
        print()

def main():
    """Main test function."""
    print("🚌 KMB Stop Name Cleaning Test Suite")
    print("Testing the function that removes stop ID codes from stop names")
    print()

    # Test the cleaning function
    success = test_stop_name_cleaning()

    # Test regex patterns
    test_regex_patterns()

    print("\n" + "=" * 60)
    if success:
        print("🎯 All tests completed successfully!")
        print("✅ The stop name cleaning function is ready for production use.")
    else:
        print("⚠️  Some tests failed. Please review the function implementation.")

    print("\n📋 Next steps:")
    print("1. Run the updated app: python app_fixed.py")
    print("2. Test with real KMB routes to see cleaned stop names")
    print("3. Check that platform indicators (A1, B2, etc.) are preserved")
    print("4. Verify that stop IDs (TA754, HO06, etc.) are removed")

if __name__ == "__main__":
    main()
