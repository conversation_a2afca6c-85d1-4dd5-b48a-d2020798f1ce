#!/usr/bin/env python3
"""
Test script to verify all bus companies (KMB, CTB, GMB) are loading correctly in localhost version.
"""

import requests
import json
import time

def test_routes_by_company(url):
    """Test and analyze routes by company."""
    print(f"\n🧪 Testing: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=60)  # Longer timeout for localhost
        elapsed = time.time() - start_time
        
        print(f"⏱️  Response time: {elapsed:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if isinstance(data, list) and len(data) > 0:
                # Count routes by company
                companies = {}
                for route in data:
                    co = route.get('co', 'Unknown')
                    companies[co] = companies.get(co, 0) + 1
                
                print(f"✅ Total routes: {len(data)}")
                print("📋 Routes by company:")
                for company, count in companies.items():
                    print(f"   - {company}: {count} routes")
                
                # Show sample routes for each company
                print("\n📝 Sample routes:")
                for company in companies.keys():
                    sample_routes = [r for r in data if r.get('co') == company][:3]
                    for route in sample_routes:
                        route_code = route.get('route', 'N/A')
                        dest = route.get('dest_en', route.get('dest_tc', 'N/A'))
                        print(f"   - {company} {route_code}: {dest}")
                
                return companies
            else:
                print("❌ No route data received")
                return {}
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            return {}
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout: Request took longer than 60 seconds")
        return {}
    except Exception as e:
        print(f"❌ Error: {e}")
        return {}

def test_warmup_endpoints(base_url):
    """Test warmup endpoints."""
    print(f"\n🔥 Testing Warmup Endpoints")
    print("=" * 40)
    
    # Test background warmup
    print("\n1. Testing background warmup...")
    try:
        response = requests.get(f"{base_url}/warmup_background", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Background warmup: {result.get('status')}")
        else:
            print(f"❌ Background warmup failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Background warmup error: {e}")
    
    # Wait a bit for background loading
    print("\n⏳ Waiting 15 seconds for background loading...")
    time.sleep(15)
    
    # Test full warmup
    print("\n2. Testing full warmup...")
    try:
        response = requests.get(f"{base_url}/warmup", timeout=60)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Full warmup: {result.get('status')}")
            print(f"   - CTB routes: {result.get('ctb_routes_count', 0)}")
            print(f"   - GMB routes: {result.get('gmb_routes_count', 0)}")
            print(f"   - Time: {result.get('elapsed_seconds', 0)}s")
        else:
            print(f"❌ Full warmup failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Full warmup error: {e}")

def test_debug_endpoints(base_url):
    """Test debug endpoints for detailed diagnostics."""
    print("\n🔍 Testing Debug Endpoints")
    print("=" * 40)

    # Test GMB debug
    print("\n1. Testing GMB debug endpoint...")
    try:
        response = requests.get(f"{base_url}/test_gmb_debug", timeout=60)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ GMB debug successful")

            # Check key results
            if 'gmb_cache_result' in data:
                cache_result = data['gmb_cache_result']
                route_count = cache_result.get('route_count', 0)
                print(f"   - GMB routes in cache: {route_count}")
                if route_count == 0:
                    print(f"   - ❌ GMB cache is empty!")
                    if 'gmb_cache_error' in data:
                        print(f"   - Error: {data['gmb_cache_error']}")
                    if 'route_codes_error' in data:
                        print(f"   - API Error: {data['route_codes_error']}")

        else:
            print(f"❌ GMB debug failed: {response.status_code}")
    except Exception as e:
        print(f"❌ GMB debug error: {e}")

    # Test CTB debug
    print("\n2. Testing CTB debug endpoint...")
    try:
        response = requests.get(f"{base_url}/test_ctb_debug", timeout=60)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ CTB debug successful")

            # Check key results
            if 'ctb_cache_result' in data:
                cache_result = data['ctb_cache_result']
                route_count = cache_result.get('route_count', 0)
                print(f"   - CTB routes in cache: {route_count}")
                if route_count == 0:
                    print(f"   - ❌ CTB cache is empty!")
                    if 'ctb_cache_error' in data:
                        print(f"   - Error: {data['ctb_cache_error']}")
                    if 'ctb_api_error' in data:
                        print(f"   - API Error: {data['ctb_api_error']}")

        else:
            print(f"❌ CTB debug failed: {response.status_code}")
    except Exception as e:
        print(f"❌ CTB debug error: {e}")

def main():
    """Test all route loading functionality for localhost."""
    base_url = "http://localhost:5000"

    print("🚌 Testing Localhost All Bus Routes Loading")
    print("=" * 60)
    
    # Test 1: Debug endpoints (detailed diagnostics)
    test_debug_endpoints(base_url)

    # Test 2: Main routes endpoint (should have all companies)
    print("\n1️⃣ Testing Main Routes (Before Warmup)")
    main_companies_before = test_routes_by_company(f"{base_url}/get_bus_routes")

    # Test 3: Warmup endpoints
    test_warmup_endpoints(base_url)

    # Test 4: Main routes endpoint after warmup
    print("\n2️⃣ Testing Main Routes (After Warmup)")
    main_companies_after = test_routes_by_company(f"{base_url}/get_bus_routes")
    
    # Test 4: Cache status
    print("\n3️⃣ Testing Cache Status")
    try:
        response = requests.get(f"{base_url}/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print("📊 Cache Statistics:")
            for key, value in stats.items():
                if 'cache' in key.lower():
                    print(f"   - {key}: {value}")
        else:
            print(f"❌ Stats failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Stats error: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    print(f"\n📋 Main Routes (Before): {sum(main_companies_before.values())} total")
    for co, count in main_companies_before.items():
        print(f"   - {co}: {count}")
    
    print(f"\n🎯 Main Routes (After): {sum(main_companies_after.values())} total")
    for co, count in main_companies_after.items():
        print(f"   - {co}: {count}")
    
    # Check if all companies are present
    expected_companies = {'KMB', 'CTB', 'GMB'}  # All companies enabled for localhost
    final_companies = set(main_companies_after.keys())
    
    if expected_companies.issubset(final_companies):
        print("\n✅ SUCCESS: All bus companies (KMB, CTB, GMB) are present!")
        print("🎉 Localhost version has complete route coverage!")
    else:
        missing = expected_companies - final_companies
        print(f"\n❌ FAILURE: Missing companies: {missing}")
        print("💡 Try running the warmup endpoints manually and test again.")
    
    # Performance analysis
    total_routes = sum(main_companies_after.values())
    if total_routes > 500:
        print(f"\n🚀 EXCELLENT: {total_routes} routes loaded (comprehensive coverage)")
    elif total_routes > 300:
        print(f"\n✅ GOOD: {total_routes} routes loaded (acceptable coverage)")
    else:
        print(f"\n⚠️  WARNING: Only {total_routes} routes loaded (may be incomplete)")
    
    print(f"\n🏁 Test complete!")

if __name__ == "__main__":
    main()
