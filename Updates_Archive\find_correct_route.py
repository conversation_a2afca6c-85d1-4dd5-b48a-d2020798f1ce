#!/usr/bin/env python3
"""
Find which route has route_id 2005253 by testing common routes
"""

import requests
import json

def find_route_with_id_2005253():
    print("Finding route with route_id 2005253...")
    print("=" * 50)
    
    # Test some common GMB routes in different regions
    test_routes = [
        ("HKI", "1"),
        ("HKI", "1A"),
        ("HKI", "2"),
        ("HKI", "3"),
        ("KLN", "1"),
        ("KLN", "2"),
        ("KLN", "3"),
        ("NT", "1"),
        ("NT", "2"),
        ("NT", "3"),
        ("NT", "301"),
        ("NT", "302"),
        ("NT", "303"),
        ("NT", "304"),
        ("NT", "305"),
        ("NT", "306"),
        ("NT", "307"),
        ("NT", "308"),
        ("NT", "309"),
        ("NT", "310"),
        ("NT", "311"),
        ("NT", "312"),
        ("NT", "314"),  # Try routes around 313
        ("NT", "315"),
        ("NT", "316"),
    ]
    
    for region, route_code in test_routes:
        try:
            url = f"https://data.etagmb.gov.hk/route/{region}/{route_code}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and isinstance(data['data'], list):
                    for route_obj in data['data']:
                        route_id = route_obj.get('route_id')
                        if route_id == 2005253:
                            print(f"🎯 FOUND route_id 2005253!")
                            print(f"   Region: {region}")
                            print(f"   Route Code: {route_code}")
                            
                            directions = route_obj.get('directions', [])
                            for direction in directions:
                                route_seq = direction.get('route_seq')
                                dest_tc = direction.get('dest_tc')
                                print(f"   Direction {route_seq}: {dest_tc}")
                            
                            return region, route_code
                            
        except Exception as e:
            continue  # Skip errors and continue searching
    
    print("❌ route_id 2005253 not found in tested routes")
    return None, None

if __name__ == "__main__":
    region, route_code = find_route_with_id_2005253()
    
    if route_code:
        print(f"\n✅ To get ETA for stop 20001219:")
        print(f"   1. Search for route '{route_code}' in region '{region}'")
        print(f"   2. Click on that route (not route 313)")
        print(f"   3. Then click on stop 20001219")
        print(f"   4. You should see the ETA data!")
    else:
        print(f"\n❓ Could not find the route. Try searching for more routes manually.")
