"""
Simplified cache management module for Vercel deployment.
Uses only in-memory caching (no Redis) for serverless compatibility.
"""

import hashlib
from typing import Optional, Any, Dict
import logging
from cachetools import TTLCache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CacheManager:
    """
    Simplified cache manager with in-memory caching only.
    Optimized for Vercel serverless environment.
    """
    
    def __init__(self):
        self.memory_cache = TTLCache(maxsize=1000, ttl=3600)  # 1 hour default TTL
        
        # Cache TTL settings (in seconds)
        self.cache_ttl = {
            'routes': 24 * 3600,      # 24 hours - routes don't change often
            'stops': 7 * 24 * 3600,   # 7 days - stop info is very static
            'eta': 30,                # 30 seconds - ETA data is real-time
            'routestops': 3600,       # 1 hour - route-stop relationships
            'default': 3600           # 1 hour default
        }
        
        logger.info("In-memory cache initialized for Vercel")
    
    def _generate_key(self, prefix: str, *args) -> str:
        """Generate a consistent cache key from prefix and arguments."""
        key_data = f"{prefix}:{':'.join(str(arg) for arg in args)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str, data_type: str = 'default') -> Optional[Any]:
        """Get value from memory cache."""
        try:
            return self.memory_cache.get(key)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, data_type: str = 'default') -> bool:
        """Set value in memory cache with appropriate TTL."""
        try:
            # Note: TTLCache doesn't support per-item TTL, uses global TTL
            self.memory_cache[key] = value
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            if key in self.memory_cache:
                del self.memory_cache[key]
            return True
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'memory_cache_size': len(self.memory_cache),
            'memory_cache_maxsize': self.memory_cache.maxsize,
            'redis_available': False,
            'cache_type': 'memory_only',
            'environment': 'vercel_serverless'
        }

# Global cache instance
cache_manager = CacheManager()

# Convenience functions for specific data types
def cache_routes(key: str, data: Any) -> bool:
    """Cache route data."""
    return cache_manager.set(key, data, 'routes')

def get_cached_routes(key: str) -> Optional[Any]:
    """Get cached route data."""
    return cache_manager.get(key, 'routes')

def cache_stops(key: str, data: Any) -> bool:
    """Cache stop data."""
    return cache_manager.set(key, data, 'stops')

def get_cached_stops(key: str) -> Optional[Any]:
    """Get cached stop data."""
    return cache_manager.get(key, 'stops')

def cache_eta(key: str, data: Any) -> bool:
    """Cache ETA data."""
    return cache_manager.set(key, data, 'eta')

def get_cached_eta(key: str) -> Optional[Any]:
    """Get cached ETA data."""
    return cache_manager.get(key, 'eta')

def cache_routestops(key: str, data: Any) -> bool:
    """Cache route-stop data."""
    return cache_manager.set(key, data, 'routestops')

def get_cached_routestops(key: str) -> Optional[Any]:
    """Get cached route-stop data."""
    return cache_manager.get(key, 'routestops')
