#!/usr/bin/env python3
"""
Find which route has route_id 2005253
"""

import requests
import json

def find_route_2005253():
    print("Searching for route_id 2005253...")
    print("=" * 50)
    
    # Get all routes first
    routes_url = "https://data.etagmb.gov.hk/route"
    try:
        response = requests.get(routes_url, timeout=15)
        if response.status_code == 200:
            routes_data = response.json()
            all_routes = routes_data.get('data', [])
            print(f"Found {len(all_routes)} total routes")
            
            # Search through all routes
            for route_info in all_routes:
                region = route_info.get('region')
                route_code = route_info.get('route_code')
                
                # Get detailed route info
                detail_url = f"https://data.etagmb.gov.hk/route/{region}/{route_code}"
                try:
                    detail_response = requests.get(detail_url, timeout=10)
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        
                        if 'data' in detail_data:
                            for route_obj in detail_data['data']:
                                route_id = route_obj.get('route_id')
                                if route_id == 2005253:
                                    print(f"🎯 FOUND route_id 2005253!")
                                    print(f"   Region: {region}")
                                    print(f"   Route Code: {route_code}")
                                    print(f"   Route Object: {json.dumps(route_obj, indent=4, ensure_ascii=False)}")
                                    
                                    # Test ETA for this route
                                    test_eta_for_route(route_id, "20001219")
                                    return
                                    
                        # Also check if this is route 313 to compare
                        if route_code == "313" and region == "NT":
                            print(f"Route 313 NT has route_id: {route_obj.get('route_id')} (not 2005253)")
                            
                except Exception as e:
                    print(f"Error getting details for {region}/{route_code}: {e}")
                    
        else:
            print(f"Error getting routes list: {response.status_code}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    print("❌ route_id 2005253 not found in any route")

def test_eta_for_route(route_id, stop_id):
    """Test ETA for the found route"""
    print(f"\n🧪 Testing ETA for route_id {route_id}, stop_id {stop_id}")
    eta_url = f"https://data.etagmb.gov.hk/eta/route-stop/{route_id}/{stop_id}"
    try:
        response = requests.get(eta_url, timeout=10)
        print(f"   ETA Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ETA Data: {json.dumps(data, indent=4, ensure_ascii=False)}")
        else:
            print(f"   ETA Error: {response.text}")
    except Exception as e:
        print(f"   ETA Exception: {e}")

if __name__ == "__main__":
    find_route_2005253()
