#!/usr/bin/env python3
"""
Test script to view all logger.info messages from load_gmb_stop_eta_optimized function
"""

import sys
import logging

# Configure logging to show all INFO messages
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Import the function
sys.path.append('.')
from app import load_gmb_stop_eta_optimized

def test_gmb_eta_with_logs():
    print("=" * 60)
    print("TESTING GMB ETA FUNCTION WITH FULL LOGGING")
    print("=" * 60)
    
    # Test parameters
    test_cases = [
        {
            'stop_id': '20001219',
            'route_id': '2005253',
            'route_seq': '1',
            'description': 'Working case (should return ETA data)'
        },
        {
            'stop_id': '20001219',
            'route_id': '2004296',
            'route_seq': '1',
            'description': 'Non-working case (route 313 - should return empty)'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*20} TEST CASE {i} {'='*20}")
        print(f"Description: {test_case['description']}")
        print(f"Parameters: stop_id={test_case['stop_id']}, route_id={test_case['route_id']}, route_seq={test_case['route_seq']}")
        print(f"{'='*60}")
        
        try:
            # Call the function
            result = load_gmb_stop_eta_optimized(
                test_case['stop_id'], 
                test_case['route_id'], 
                test_case['route_seq']
            )
            
            print(f"\n📊 RESULT SUMMARY:")
            print(f"   Type: {type(result)}")
            print(f"   Length: {len(result) if result else 0}")
            print(f"   Content: {result}")
            
            if result:
                print(f"\n✅ SUCCESS - ETA data returned!")
                for j, eta_entry in enumerate(result, 1):
                    print(f"   ETA {j}: {eta_entry[1]} - {eta_entry[2]}")
            else:
                print(f"\n❌ NO DATA - Function returned empty/None")
                
        except Exception as e:
            print(f"\n💥 ERROR: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n{'='*60}")

if __name__ == "__main__":
    test_gmb_eta_with_logs()
