#!/usr/bin/env python3
"""
Installation and testing script for the optimized bus route app.
This script will:
1. Install required dependencies
2. Test the optimizations
3. Compare performance between original and optimized versions
"""

import subprocess
import sys
import time
import requests
import json
from concurrent.futures import ThreadPoolExecutor
import statistics

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def test_redis_connection():
    """Test Redis connection (optional)."""
    try:
        import redis
        client = redis.Redis(host='localhost', port=6379, db=0)
        client.ping()
        print("✅ Redis connection successful!")
        return True
    except Exception as e:
        print(f"⚠️  Redis not available: {e}")
        print("   App will use in-memory caching only.")
        return False

def start_app(app_file, port):
    """Start Flask app in background."""
    import subprocess
    import os
    
    env = os.environ.copy()
    env['FLASK_APP'] = app_file
    env['FLASK_ENV'] = 'development'
    
    process = subprocess.Popen([
        sys.executable, app_file
    ], env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait a bit for the app to start
    time.sleep(3)
    return process

def test_endpoint_performance(base_url, endpoint, params=None, num_requests=5):
    """Test endpoint performance."""
    url = f"{base_url}{endpoint}"
    if params:
        url += "?" + "&".join([f"{k}={v}" for k, v in params.items()])
    
    times = []
    success_count = 0
    
    for i in range(num_requests):
        try:
            start_time = time.time()
            response = requests.get(url, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                times.append(end_time - start_time)
                success_count += 1
            else:
                print(f"   Request {i+1}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   Request {i+1}: Error - {e}")
    
    if times:
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        return {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'success_rate': success_count / num_requests,
            'times': times
        }
    else:
        return None

def run_performance_tests():
    """Run performance tests on both versions."""
    print("\n🧪 Running Performance Tests...")
    
    # Test endpoints
    test_cases = [
        {
            'name': 'Get Bus Routes',
            'endpoint': '/get_bus_routes',
            'params': None
        },
        {
            'name': 'Get KMB Route Stops',
            'endpoint': '/get_kmb_bus_routestop',
            'params': {'r': '1', 'b': 'O', 's': '1'}
        },
        {
            'name': 'Get KMB Stop ETA',
            'endpoint': '/get_kmb_stop_eta',
            'params': {'id': 'HO06-S-1250-0', 'r': '1', 'b': 'O'}
        }
    ]
    
    results = {}
    
    # Test optimized version
    print("\n📊 Testing Optimized Version (port 5000)...")
    optimized_process = None
    try:
        optimized_process = start_app('app_fixed.py', 5000)
        
        for test_case in test_cases:
            print(f"   Testing: {test_case['name']}")
            result = test_endpoint_performance(
                'http://localhost:5000',
                test_case['endpoint'],
                test_case['params'],
                num_requests=3
            )
            if result:
                results[f"optimized_{test_case['name']}"] = result
                print(f"     Avg: {result['avg_time']:.3f}s, Success: {result['success_rate']:.1%}")
            else:
                print(f"     ❌ Failed")
                
    except Exception as e:
        print(f"Error testing optimized version: {e}")
    finally:
        if optimized_process:
            optimized_process.terminate()
            optimized_process.wait()
    
    # Test original version
    print("\n📊 Testing Original Version (port 5001)...")
    original_process = None
    try:
        # Modify original app to run on different port
        with open('app.py', 'r') as f:
            original_content = f.read()
        
        modified_content = original_content.replace("app.run()", "app.run(port=5001)")
        with open('app_original_test.py', 'w') as f:
            f.write(modified_content)
        
        original_process = start_app('app_original_test.py', 5001)
        
        for test_case in test_cases:
            print(f"   Testing: {test_case['name']}")
            result = test_endpoint_performance(
                'http://localhost:5001',
                test_case['endpoint'],
                test_case['params'],
                num_requests=3
            )
            if result:
                results[f"original_{test_case['name']}"] = result
                print(f"     Avg: {result['avg_time']:.3f}s, Success: {result['success_rate']:.1%}")
            else:
                print(f"     ❌ Failed")
                
    except Exception as e:
        print(f"Error testing original version: {e}")
    finally:
        if original_process:
            original_process.terminate()
            original_process.wait()
    
    # Compare results
    print("\n📈 Performance Comparison:")
    for test_case in test_cases:
        opt_key = f"optimized_{test_case['name']}"
        orig_key = f"original_{test_case['name']}"
        
        if opt_key in results and orig_key in results:
            opt_time = results[opt_key]['avg_time']
            orig_time = results[orig_key]['avg_time']
            improvement = ((orig_time - opt_time) / orig_time) * 100
            
            print(f"   {test_case['name']}:")
            print(f"     Original: {orig_time:.3f}s")
            print(f"     Optimized: {opt_time:.3f}s")
            print(f"     Improvement: {improvement:.1f}%")
    
    return results

def test_cache_functionality():
    """Test cache functionality."""
    print("\n🗄️  Testing Cache Functionality...")
    
    try:
        # Test cache stats endpoint
        response = requests.get('http://localhost:5000/cache_stats', timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"   Cache Stats: {json.dumps(stats, indent=2)}")
            return True
        else:
            print(f"   ❌ Cache stats failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Cache test error: {e}")
        return False

def main():
    """Main function."""
    print("🚌 Bus Route App Optimization Setup and Testing")
    print("=" * 50)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Installation failed. Exiting.")
        return
    
    # Step 2: Test Redis (optional)
    test_redis_connection()
    
    # Step 3: Run performance tests
    try:
        results = run_performance_tests()
        
        # Step 4: Test cache functionality
        print("\n🔄 Starting optimized app for cache testing...")
        process = start_app('app_fixed.py', 5000)
        try:
            test_cache_functionality()
        finally:
            process.terminate()
            process.wait()
        
        print("\n✅ Testing completed!")
        print("\n📋 Summary:")
        print("   - Optimized app includes caching and async API calls")
        print("   - Check the performance comparison above")
        print("   - Use 'python app_fixed.py' to run the optimized version")
        print("   - Monitor cache stats at /cache_stats endpoint")
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing error: {e}")

if __name__ == "__main__":
    main()
