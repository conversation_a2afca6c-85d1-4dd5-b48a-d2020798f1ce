#!/usr/bin/env python3
"""
Test stop 20001219 to see what routes serve it
"""

import requests
import json

def test_stop_routes():
    print("Testing stop 20001219...")
    print("=" * 50)
    
    # Test a few different route_ids to see which ones work with this stop
    test_route_ids = [
        "2004296",  # The actual route_id for route 313 NT
        "2005253",  # The route_id you mentioned
        "2005254",  # Try nearby IDs
        "2005252",
        "2004295",
        "2004297"
    ]
    
    stop_id = "20001219"
    
    for route_id in test_route_ids:
        print(f"\nTesting route_id: {route_id}")
        eta_url = f"https://data.etagmb.gov.hk/eta/route-stop/{route_id}/{stop_id}"
        
        try:
            response = requests.get(eta_url, timeout=10)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                eta_data = data.get('data', [])
                print(f"  ✅ SUCCESS! Data entries: {len(eta_data)}")
                
                for item in eta_data:
                    route_seq = item.get('route_seq')
                    enabled = item.get('enabled')
                    eta_count = len(item.get('eta', []))
                    print(f"    route_seq={route_seq}, enabled={enabled}, eta_entries={eta_count}")
                    
                    if eta_count > 0:
                        print(f"    📍 ETA data available!")
                        # Show first ETA entry
                        first_eta = item.get('eta', [])[0]
                        print(f"    First ETA: {json.dumps(first_eta, indent=6, ensure_ascii=False)}")
                        
            elif response.status_code == 404:
                print(f"  ❌ 404 - Route/Stop combination not found")
            else:
                print(f"  ❌ Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"  ❌ Exception: {e}")

def test_gmb_route_stops():
    """Test getting route stops for route 313 to see what stops are available"""
    print(f"\n" + "=" * 50)
    print("Testing GMB route stops for route 313...")
    
    # Try to get route stops for route 313 (route_id 2004296)
    route_id = "2004296"
    route_seq = "1"  # Try direction 1
    
    stops_url = f"https://data.etagmb.gov.hk/route-stop/{route_id}/{route_seq}"
    
    try:
        response = requests.get(stops_url, timeout=10)
        print(f"Route stops status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            stops_data = data.get('data', [])
            print(f"Found {len(stops_data)} stops for route 313 direction 1")
            
            # Look for stop 20001219
            for stop in stops_data:
                stop_id = stop.get('stop_id')
                stop_name = stop.get('name_tc')
                print(f"  Stop: {stop_id} - {stop_name}")
                
                if stop_id == "20001219":
                    print(f"  🎯 FOUND stop 20001219 in route 313!")
                    
        else:
            print(f"Error getting route stops: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_stop_routes()
    test_gmb_route_stops()
