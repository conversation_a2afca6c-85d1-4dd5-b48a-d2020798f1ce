"""
Async HTTP client module for optimized API calls to bus data services.
Provides concurrent request handling and connection pooling.
"""

import asyncio
import aiohttp
import time
from typing import List, Dict, Any, Optional, Tuple
import logging
from asyncio_throttle import Throttler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AsyncBusAPIClient:
    """
    Async HTTP client optimized for bus API calls with:
    - Connection pooling
    - Request throttling
    - Timeout handling
    - Retry logic
    - Concurrent request batching
    """

    def __init__(self, proxies: Dict[str, str] = None):
        self.proxies = proxies or {}
        self.session = None
        self.throttler = Throttler(rate_limit=10, period=1.0)  # 10 requests per second

        # Timeout settings
        self.timeout = aiohttp.ClientTimeout(
            total=30,      # Total timeout
            connect=10,    # Connection timeout
            sock_read=10   # Socket read timeout
        )

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()

    async def start_session(self):
        """Initialize the aiohttp session with optimized settings."""
        if self.session is None:
            connector = aiohttp.TCPConnector(
                limit=100,           # Total connection pool size
                limit_per_host=30,   # Max connections per host
                ttl_dns_cache=300,   # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=self.timeout,
                trust_env=True
            )
            logger.info("Async HTTP session initialized")

    async def close_session(self):
        """Close the aiohttp session."""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Async HTTP session closed")

    async def fetch_json(self, url: str, max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        Fetch JSON data from URL with retry logic and throttling.
        """
        if not self.session:
            await self.start_session()

        async with self.throttler:
            for attempt in range(max_retries):
                try:
                    async with self.session.get(url, proxy=self.proxies.get('https')) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            logger.warning(f"HTTP {response.status} for URL: {url}")

                except asyncio.TimeoutError:
                    logger.warning(f"Timeout for URL: {url} (attempt {attempt + 1})")
                except Exception as e:
                    logger.error(f"Error fetching {url} (attempt {attempt + 1}): {e}")

                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff

            logger.error(f"Failed to fetch {url} after {max_retries} attempts")
            return None

    async def fetch_multiple_json(self, urls: List[str]) -> List[Tuple[str, Optional[Dict[str, Any]]]]:
        """
        Fetch multiple URLs concurrently and return results.
        Returns list of (url, result) tuples.
        """
        if not self.session:
            await self.start_session()

        tasks = [self.fetch_json(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Pair URLs with their results
        return [(url, result if not isinstance(result, Exception) else None)
                for url, result in zip(urls, results)]

    async def fetch_stop_details_batch(self, stop_ids: List[str], api_base: str) -> Dict[str, Dict[str, Any]]:
        """
        Fetch stop details for multiple stop IDs concurrently.
        Returns dict mapping stop_id to stop data.
        """
        urls = [f"{api_base}/stop/{stop_id}" for stop_id in stop_ids]
        results = await self.fetch_multiple_json(urls)

        stop_data = {}
        for stop_id, (url, result) in zip(stop_ids, results):
            if result and result.get('data'):
                stop_data[stop_id] = result['data']
            else:
                logger.warning(f"Failed to fetch stop data for {stop_id}")

        return stop_data

# Global async client instance
_global_client = None

async def get_async_client(proxies: Dict[str, str] = None) -> AsyncBusAPIClient:
    """Get or create global async client instance."""
    global _global_client
    if _global_client is None or _global_client.session is None or _global_client.session.closed:
        _global_client = AsyncBusAPIClient(proxies)
        await _global_client.start_session()
    return _global_client

async def close_global_client():
    """Close global async client."""
    global _global_client
    if _global_client:
        try:
            await _global_client.close_session()
        except Exception as e:
            logger.error(f"Error closing global client: {e}")
        finally:
            _global_client = None

# Convenience functions for common API patterns
async def fetch_kmb_route_stops(route: str, bound_type: str, service_type: str, proxies: Dict[str, str] = None) -> Optional[List[Dict[str, Any]]]:
    """Fetch KMB route stops asynchronously."""
    client = await get_async_client(proxies)
    url = f"https://data.etabus.gov.hk/v1/transport/kmb/route-stop/{route}/{bound_type}/{service_type}"
    result = await client.fetch_json(url)
    return result.get('data') if result else None

async def fetch_ctb_route_stops(route: str, bound_type: str, proxies: Dict[str, str] = None) -> Optional[List[Dict[str, Any]]]:
    """Fetch CTB route stops asynchronously."""
    client = await get_async_client(proxies)
    url = f"https://rt.data.gov.hk/v2/transport/citybus/route-stop/CTB/{route}/{bound_type}"
    result = await client.fetch_json(url)
    return result.get('data') if result else None

async def fetch_kmb_stops_batch(stop_ids: List[str], proxies: Dict[str, str] = None) -> Dict[str, Dict[str, Any]]:
    """Fetch multiple KMB stop details concurrently."""
    client = await get_async_client(proxies)
    return await client.fetch_stop_details_batch(stop_ids, "https://data.etabus.gov.hk/v1/transport/kmb")

async def fetch_ctb_stops_batch(stop_ids: List[str], proxies: Dict[str, str] = None) -> Dict[str, Dict[str, Any]]:
    """Fetch multiple CTB stop details concurrently."""
    client = await get_async_client(proxies)
    return await client.fetch_stop_details_batch(stop_ids, "https://rt.data.gov.hk/v2/transport/citybus")

async def fetch_kmb_eta(stop_id: str, proxies: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
    """Fetch KMB ETA data asynchronously."""
    client = await get_async_client(proxies)
    url = f"https://data.etabus.gov.hk/v1/transport/kmb/stop-eta/{stop_id}"
    return await client.fetch_json(url)

async def fetch_ctb_eta(stop_id: str, route: str, proxies: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
    """Fetch CTB ETA data asynchronously."""
    client = await get_async_client(proxies)
    url = f"https://rt.data.gov.hk/v2/transport/citybus/eta/CTB/{stop_id}/{route}"
    return await client.fetch_json(url)
