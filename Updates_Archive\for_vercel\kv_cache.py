"""
KV Cache module for GMB route data using Vercel KV (Redis).
Handles caching and retrieval of GMB routes to avoid timeout issues.
"""

import os
import json
import logging
from typing import Optional, List, Dict, Any
import redis
import time

# Configure logging
logger = logging.getLogger(__name__)

class KVCacheManager:
    """
    Manages GMB route data in Vercel KV (Redis) for performance optimization.
    """
    
    def __init__(self):
        self.redis_client = None
        self.kv_available = False
        self._initialize_kv()
    
    def _initialize_kv(self):
        """Initialize Vercel KV connection."""
        try:
            # Vercel KV environment variables
            kv_url = os.getenv('KV_URL')
            kv_rest_api_url = os.getenv('KV_REST_API_URL')
            kv_rest_api_token = os.getenv('KV_REST_API_TOKEN')
            kv_rest_api_read_only_token = os.getenv('KV_REST_API_READ_ONLY_TOKEN')
            
            if kv_url:
                # Use direct Redis connection if KV_URL is available
                self.redis_client = redis.from_url(kv_url)
                # Test connection
                self.redis_client.ping()
                self.kv_available = True
                logger.info("✅ Vercel KV (Redis) connection established successfully")
            else:
                logger.warning("⚠️ Vercel KV environment variables not found - KV cache disabled")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize Vercel KV: {e}")
            self.kv_available = False
    
    def is_available(self) -> bool:
        """Check if KV cache is available."""
        return self.kv_available and self.redis_client is not None
    
    def get_gmb_routes(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get GMB routes from KV cache.
        Returns None if cache miss or KV unavailable.
        """
        if not self.is_available():
            logger.info("🔄 KV cache not available - will fallback to KMB+CTB only")
            return None
        
        try:
            # Try to get cached GMB routes
            cached_data = self.redis_client.get('gmb:routes:all')
            
            if cached_data:
                routes_data = json.loads(cached_data)
                cache_timestamp = routes_data.get('timestamp', 0)
                routes = routes_data.get('routes', [])
                
                # Calculate cache age
                cache_age_days = (time.time() - cache_timestamp) / (24 * 3600)
                
                logger.info(f"✅ GMB routes cache HIT - {len(routes)} routes (cached {cache_age_days:.1f} days ago)")
                return routes
            else:
                logger.info("❌ GMB routes cache MISS - cache is empty")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error reading GMB routes from KV cache: {e}")
            return None
    
    def set_gmb_routes(self, routes: List[Dict[str, Any]]) -> bool:
        """
        Store GMB routes in KV cache.
        Returns True if successful, False otherwise.
        """
        if not self.is_available():
            logger.warning("⚠️ KV cache not available - cannot store GMB routes")
            return False
        
        try:
            # Prepare data with timestamp
            cache_data = {
                'routes': routes,
                'timestamp': time.time(),
                'count': len(routes)
            }
            
            # Store in KV with 35-day expiration (longer than monthly refresh)
            expiration_seconds = 35 * 24 * 3600  # 35 days
            
            self.redis_client.setex(
                'gmb:routes:all',
                expiration_seconds,
                json.dumps(cache_data)
            )
            
            logger.info(f"✅ GMB routes cached successfully - {len(routes)} routes stored in KV")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error storing GMB routes in KV cache: {e}")
            return False
    
    def clear_gmb_cache(self) -> bool:
        """
        Clear GMB routes cache.
        Returns True if successful, False otherwise.
        """
        if not self.is_available():
            logger.warning("⚠️ KV cache not available - cannot clear cache")
            return False
        
        try:
            deleted_count = self.redis_client.delete('gmb:routes:all')
            if deleted_count > 0:
                logger.info("✅ GMB routes cache cleared successfully")
                return True
            else:
                logger.info("ℹ️ GMB routes cache was already empty")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error clearing GMB routes cache: {e}")
            return False
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about the current cache status.
        """
        info = {
            'kv_available': self.is_available(),
            'cache_type': 'vercel_kv_redis'
        }
        
        if self.is_available():
            try:
                cached_data = self.redis_client.get('gmb:routes:all')
                if cached_data:
                    routes_data = json.loads(cached_data)
                    cache_timestamp = routes_data.get('timestamp', 0)
                    routes_count = routes_data.get('count', 0)
                    cache_age_days = (time.time() - cache_timestamp) / (24 * 3600)
                    
                    info.update({
                        'cache_status': 'hit',
                        'routes_count': routes_count,
                        'cache_age_days': round(cache_age_days, 1),
                        'cache_timestamp': cache_timestamp
                    })
                else:
                    info.update({
                        'cache_status': 'miss',
                        'routes_count': 0,
                        'cache_age_days': 0
                    })
            except Exception as e:
                info.update({
                    'cache_status': 'error',
                    'error': str(e)
                })
        
        return info

# Global KV cache instance
kv_cache = KVCacheManager()
