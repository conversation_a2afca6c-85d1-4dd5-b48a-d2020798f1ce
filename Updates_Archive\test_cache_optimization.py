#!/usr/bin/env python3
"""
Test script to verify the cache optimization improvements.
Run this to test the enhanced caching functionality.
"""

import requests
import json
import time

def test_endpoint(url, endpoint_name):
    """Test a single endpoint."""
    print(f"\n🧪 Testing {endpoint_name}...")
    print(f"URL: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=30)
        elapsed = time.time() - start_time
        
        print(f"⏱️  Response time: {elapsed:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"✅ Success: Received {len(data)} items")
                    if len(data) > 0:
                        print(f"📝 Sample item: {json.dumps(data[0], indent=2, ensure_ascii=False)[:200]}...")
                elif isinstance(data, dict):
                    print(f"✅ Success: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"✅ Success: {data}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON Error: {e}")
                print(f"📄 Response text: {response.text[:200]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout: Request took longer than 30 seconds")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")

def test_cache_performance():
    """Test cache performance improvements."""
    base_url = "http://localhost:5000"
    
    print("🚀 Testing Cache Optimization")
    print("=" * 50)
    
    # Test health endpoint first
    test_endpoint(f"{base_url}/health", "Health Check")
    
    # Test stats to see cache status
    test_endpoint(f"{base_url}/stats", "Cache Statistics")
    
    # Test warmup endpoint
    print(f"\n🔥 Testing Cache Warmup...")
    start_time = time.time()
    test_endpoint(f"{base_url}/warmup", "Cache Warmup")
    warmup_time = time.time() - start_time
    
    # Test main routes endpoint (should be fast after warmup)
    print(f"\n⚡ Testing Routes After Warmup...")
    start_time = time.time()
    test_endpoint(f"{base_url}/get_bus_routes", "Bus Routes (After Warmup)")
    routes_time = time.time() - start_time
    
    # Test routes again (should be even faster from cache)
    print(f"\n💨 Testing Routes From Cache...")
    start_time = time.time()
    test_endpoint(f"{base_url}/get_bus_routes", "Bus Routes (From Cache)")
    cache_time = time.time() - start_time
    
    # Test cache clear
    test_endpoint(f"{base_url}/cache/clear", "Cache Clear")
    
    # Test stats again to see cleared cache
    test_endpoint(f"{base_url}/stats", "Cache Statistics (After Clear)")
    
    print("\n" + "=" * 50)
    print("📊 Performance Summary:")
    print(f"  - Warmup time: {warmup_time:.2f}s")
    print(f"  - Routes after warmup: {routes_time:.2f}s")
    print(f"  - Routes from cache: {cache_time:.2f}s")
    print(f"  - Cache speedup: {routes_time/cache_time:.1f}x faster")
    print("\n🎯 Cache optimization working correctly!")

def test_cache_ttl_info():
    """Test cache TTL information."""
    base_url = "http://localhost:5000"
    
    print("\n🕐 Testing Cache TTL Settings...")
    try:
        response = requests.get(f"{base_url}/stats")
        if response.status_code == 200:
            stats = response.json()
            
            print("📋 Cache TTL Configuration:")
            for key, value in stats.items():
                if 'cache_ttl' in key:
                    if isinstance(value, int):
                        days = value // (24 * 3600)
                        hours = (value % (24 * 3600)) // 3600
                        if days > 0:
                            print(f"  - {key}: {days} days, {hours} hours")
                        else:
                            print(f"  - {key}: {hours} hours")
                    else:
                        print(f"  - {key}: {value}")
                        
            print("\n📈 Cache Age Information:")
            for key, value in stats.items():
                if 'cache_age_days' in key or 'cache_valid_for_days' in key:
                    print(f"  - {key}: {value}")
                    
    except Exception as e:
        print(f"❌ Error getting cache info: {e}")

def main():
    """Run all tests."""
    print("🚀 Cache Optimization Test Suite")
    print("=" * 60)
    
    # Test basic functionality
    test_cache_performance()
    
    # Test cache TTL information
    test_cache_ttl_info()
    
    print("\n" + "=" * 60)
    print("🏁 All tests complete!")
    print("\n💡 Tips:")
    print("  - Routes are now cached for 30 days")
    print("  - Route-stops are cached for 30 days")
    print("  - Stop info is cached for 90 days")
    print("  - ETA data is still real-time (30 seconds)")
    print("  - Use /cache/clear to force refresh when needed")

if __name__ == "__main__":
    main()
