"""
Optimized Flask app for bus route searching with caching and async API calls.
Performance improvements:
1. Redis caching with in-memory fallback
2. Async HTTP requests with connection pooling
3. Concurrent API calls for stop details
4. Request optimization and retry logic
"""

from flask import Flask, jsonify, request
from datetime import datetime
from math import radians, sin, cos, sqrt, atan2
import json
import asyncio
import logging
from functools import wraps
import time

# Import our optimization modules
from cache_manager import (
    cache_manager, cache_routes, get_cached_routes,
    cache_stops, get_cached_stops, cache_eta, get_cached_eta,
    cache_routestops, get_cached_routestops
)
from async_http_client import (
    get_async_client, close_global_client,
    fetch_kmb_route_stops, fetch_ctb_route_stops,
    fetch_kmb_stops_batch, fetch_ctb_stops_batch,
    fetch_kmb_eta, fetch_ctb_eta
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
proxies = {
    'http': 'http://proxy2.gld.hksarg:8080',
    'https': 'http://proxy2.gld.hksarg:8080',
}

# Global variables (to be refactored later)
route = ''
bound = ''

# Cache for CTB data loaded at startup
ctb_routes_cache = None

# Global event loop for async operations
_global_loop = None
_loop_thread = None

def get_or_create_event_loop():
    """Get or create a global event loop running in a separate thread."""
    global _global_loop, _loop_thread

    if _global_loop is None or _global_loop.is_closed():
        import threading

        def run_loop():
            global _global_loop
            _global_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(_global_loop)
            _global_loop.run_forever()

        _loop_thread = threading.Thread(target=run_loop, daemon=True)
        _loop_thread.start()

        # Wait for loop to be ready
        import time
        while _global_loop is None:
            time.sleep(0.01)

    return _global_loop

def async_route(f):
    """Decorator to run async functions in Flask routes using a persistent event loop."""
    @wraps(f)
    def wrapper(*args, **kwargs):
        loop = get_or_create_event_loop()
        future = asyncio.run_coroutine_threadsafe(f(*args, **kwargs), loop)
        try:
            return future.result(timeout=30)  # 30 second timeout
        except Exception as e:
            logger.error(f"Async route error: {e}")
            raise
    return wrapper

def load_ctb_data_at_startup():
    """Load CTB data once at startup and cache it."""
    global ctb_routes_cache
    try:
        with open('ctb_data.json', encoding='utf-8') as json_file:
            ctb_routes_cache = json.load(json_file)
        logger.info(f"Loaded {len(ctb_routes_cache)} CTB routes at startup")
    except Exception as e:
        logger.error(f"Error loading CTB routes data: {e}")
        ctb_routes_cache = []

@app.route('/')
def index():
    return open('index.html', encoding="utf-8").read()

@app.route('/get_bus_routes')
@async_route
async def get_bus_routes():
    """Get all bus routes with caching."""
    start_time = time.time()

    # Check cache first
    cache_key = cache_manager._generate_key('all_routes')
    cached_routes = get_cached_routes(cache_key)

    if cached_routes:
        logger.info(f"Routes served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_routes)

    # Fetch fresh data
    bus_routes = await load_bus_routes_async()

    # Cache the result
    if bus_routes:
        cache_routes(cache_key, bus_routes)

    logger.info(f"Routes loaded and cached in {time.time() - start_time:.3f}s")
    return jsonify(bus_routes)

async def load_bus_routes_async():
    """Load bus routes asynchronously with caching."""
    try:
        # Fetch KMB routes
        client = await get_async_client(proxies)
        url = "https://data.etabus.gov.hk/v1/transport/kmb/route/"
        kmb_result = await client.fetch_json(url)

        if kmb_result and kmb_result.get('data'):
            kmb_routes_data = kmb_result['data']
            # Add company identifier
            for route in kmb_routes_data:
                route["co"] = "KMB"
        else:
            logger.error("Error loading KMB routes data")
            kmb_routes_data = []

        # Use cached CTB data
        global ctb_routes_cache
        ctb_routes_data = ctb_routes_cache or []

        # Combine data
        combined_data = kmb_routes_data + ctb_routes_data
        return combined_data

    except Exception as e:
        logger.error(f"Error in load_bus_routes_async: {e}")
        return []

@app.route('/get_kmb_bus_routestop')
@async_route
async def get_kmb_bus_routestop():
    """Get KMB route stops with caching and async optimization."""
    start_time = time.time()

    # Extract parameters
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    service_type = request.args.get('s')
    user_lat = request.args.get('lat')
    user_lon = request.args.get('lon')

    # Generate cache key
    cache_key = cache_manager._generate_key(
        'kmb_routestop', route_param, bound_param, service_type, user_lat, user_lon
    )

    # Check cache
    cached_result = get_cached_routestops(cache_key)
    if cached_result:
        logger.info(f"KMB route stops served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_result)

    # Load fresh data
    bus_routestop = await load_kmb_bus_routestop_async(
        route_param, bound_param, service_type, user_lat, user_lon
    )

    # Cache result
    if bus_routestop:
        cache_routestops(cache_key, bus_routestop)

    logger.info(f"KMB route stops loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_routestop)

async def load_kmb_bus_routestop_async(route_param, bound_param, service_type, user_lat, user_lon):
    """Load KMB route stops with concurrent API calls."""
    global route, bound
    route = route_param
    bound = bound_param

    boundtype = 'inbound' if bound_param == 'I' else 'outbound'

    try:
        # Fetch route stops
        routestop_data = await fetch_kmb_route_stops(route_param, boundtype, service_type, proxies)

        if not routestop_data:
            logger.error("Error loading KMB bus routestop data")
            return None

        # Extract stop IDs
        stop_ids = [row["stop"] for row in routestop_data]

        # Fetch all stop details concurrently
        stop_details = await fetch_kmb_stops_batch(stop_ids, proxies)

        # Build result list
        stop_list = []
        for row in routestop_data:
            stop_id = row["stop"]
            stop_seq = row["seq"]

            if stop_id in stop_details:
                stop_data = stop_details[stop_id]
                km = ''
                if user_lat and user_lon:
                    km = haversine(
                        float(user_lat), float(user_lon),
                        float(stop_data["lat"]), float(stop_data["long"])
                    )
                stop_obj = [stop_id, stop_data["name_tc"], stop_seq, km]
                stop_list.append(stop_obj)
            else:
                logger.warning(f"Missing stop data for {stop_id}")

        return stop_list

    except Exception as e:
        logger.error(f"Error in load_kmb_bus_routestop_async: {e}")
        return None

@app.route('/get_kmb_stop_eta')
@async_route
async def get_kmb_stop_eta():
    """Get KMB stop ETA with caching."""
    start_time = time.time()

    # Extract parameters
    stop_id = request.args.get('id')
    route_param = request.args.get('r')
    bound_param = request.args.get('b')

    # Generate cache key
    cache_key = cache_manager._generate_key('kmb_eta', stop_id, route_param, bound_param)

    # Check cache (short TTL for ETA data)
    cached_eta = get_cached_eta(cache_key)
    if cached_eta:
        logger.info(f"KMB ETA served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_eta)

    # Load fresh ETA data
    bus_stop_eta = await load_kmb_stop_eta_async(stop_id, route_param, bound_param)

    # Cache result
    if bus_stop_eta:
        cache_eta(cache_key, bus_stop_eta)

    logger.info(f"KMB ETA loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_stop_eta)

async def load_kmb_stop_eta_async(stop_id, route_param, bound_param):
    """Load KMB stop ETA asynchronously."""
    global route, bound
    route = route_param
    bound = bound_param

    try:
        eta_data = await fetch_kmb_eta(stop_id, proxies)

        if not eta_data or not eta_data.get('data'):
            logger.error("Error loading KMB bus stop eta data")
            return None

        eta_list = []
        for row in eta_data['data']:
            if (row["route"] == route_param and
                row["dir"] == bound_param and
                row["service_type"] == 1):

                time_eta = ''
                eta = ''
                rmk_tc = row["rmk_tc"]
                time_current = datetime.strptime(row["data_timestamp"], '%Y-%m-%dT%H:%M:%S+08:00')

                if row["eta"] and row["eta"] != '':
                    time_eta = datetime.strptime(row["eta"], '%Y-%m-%dT%H:%M:%S+08:00')
                    delta = time_eta - time_current
                    minutes, seconds = divmod(delta.total_seconds(), 60)
                    eta = f"{int(minutes)} minutes {int(seconds)} seconds"
                    rmk_tc = '九巴'
                    if delta.total_seconds() < 0:
                        rmk_tc = '已到達'

                dt_current_str = time_current.strftime("%Y-%m-%d %H:%M:%S")
                eta_obj = [time_eta, eta, rmk_tc, dt_current_str]
                eta_list.append(eta_obj)

        return eta_list

    except Exception as e:
        logger.error(f"Error in load_kmb_stop_eta_async: {e}")
        return None

def haversine(lat1, lon1, lat2, lon2):
    """Calculate the great circle distance between two points on earth."""
    R = 6371  # Earth radius in km
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    return R * c

@app.route('/get_ctb_bus_routestop')
@async_route
async def get_ctb_bus_routestop():
    """Get CTB route stops with caching and async optimization."""
    start_time = time.time()

    # Extract parameters
    route_param = request.args.get('r')
    bound_param = request.args.get('b')
    user_lat = request.args.get('lat')
    user_lon = request.args.get('lon')

    # Generate cache key
    cache_key = cache_manager._generate_key(
        'ctb_routestop', route_param, bound_param, user_lat, user_lon
    )

    # Check cache
    cached_result = get_cached_routestops(cache_key)
    if cached_result:
        logger.info(f"CTB route stops served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_result)

    # Load fresh data
    bus_routestop = await load_ctb_bus_routestop_async(
        route_param, bound_param, user_lat, user_lon
    )

    # Cache result
    if bus_routestop:
        cache_routestops(cache_key, bus_routestop)

    logger.info(f"CTB route stops loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_routestop)

async def load_ctb_bus_routestop_async(route_param, bound_param, user_lat, user_lon):
    """Load CTB route stops with concurrent API calls."""
    global route, bound
    route = route_param
    bound = bound_param

    boundtype = 'inbound' if bound_param == 'I' else 'outbound'

    try:
        # Fetch route stops
        routestop_data = await fetch_ctb_route_stops(route_param, boundtype, proxies)

        if not routestop_data:
            logger.error("Error loading CTB bus routestop data")
            return None

        # Extract stop IDs
        stop_ids = [row["stop"] for row in routestop_data]

        # Fetch all stop details concurrently
        stop_details = await fetch_ctb_stops_batch(stop_ids, proxies)

        # Build result list
        stop_list = []
        for row in routestop_data:
            stop_id = row["stop"]
            stop_seq = row["seq"]

            if stop_id in stop_details:
                stop_data = stop_details[stop_id]
                km = ''
                if user_lat and user_lon:
                    km = haversine(
                        float(user_lat), float(user_lon),
                        float(stop_data["lat"]), float(stop_data["long"])
                    )
                stop_obj = [stop_id, stop_data["name_tc"], stop_seq, km]
                stop_list.append(stop_obj)
            else:
                logger.warning(f"Missing CTB stop data for {stop_id}")

        return stop_list

    except Exception as e:
        logger.error(f"Error in load_ctb_bus_routestop_async: {e}")
        return None

@app.route('/get_ctb_stop_eta')
@async_route
async def get_ctb_stop_eta():
    """Get CTB stop ETA with caching."""
    start_time = time.time()

    # Extract parameters
    stop_id = request.args.get('id')
    route_param = request.args.get('r')

    # Generate cache key
    cache_key = cache_manager._generate_key('ctb_eta', stop_id, route_param)

    # Check cache (short TTL for ETA data)
    cached_eta = get_cached_eta(cache_key)
    if cached_eta:
        logger.info(f"CTB ETA served from cache in {time.time() - start_time:.3f}s")
        return jsonify(cached_eta)

    # Load fresh ETA data
    bus_stop_eta = await load_ctb_stop_eta_async(stop_id, route_param)

    # Cache result
    if bus_stop_eta:
        cache_eta(cache_key, bus_stop_eta)

    logger.info(f"CTB ETA loaded in {time.time() - start_time:.3f}s")
    return jsonify(bus_stop_eta)

async def load_ctb_stop_eta_async(stop_id, route_param):
    """Load CTB stop ETA asynchronously."""
    try:
        eta_data = await fetch_ctb_eta(stop_id, route_param, proxies)

        if not eta_data or not eta_data.get('data'):
            logger.error("Error loading CTB bus stop eta data")
            return None

        eta_list = []
        for row in eta_data['data']:
            if row["route"] == route_param:
                time_eta = ''
                eta = ''
                rmk_tc = '城巴'
                time_current = datetime.strptime(row["data_timestamp"], '%Y-%m-%dT%H:%M:%S+08:00')

                if row["eta"] and row["eta"] != '':
                    time_eta = datetime.strptime(row["eta"], '%Y-%m-%dT%H:%M:%S+08:00')
                    delta = time_eta - time_current
                    minutes, seconds = divmod(delta.total_seconds(), 60)
                    eta = f"{int(minutes)} minutes {int(seconds)} seconds"
                    if delta.total_seconds() < 0:
                        rmk_tc = "已到達"

                dt_current_str = time_current.strftime("%Y-%m-%d %H:%M:%S")
                eta_obj = [time_eta, eta, rmk_tc, dt_current_str]
                eta_list.append(eta_obj)

        return eta_list

    except Exception as e:
        logger.error(f"Error in load_ctb_stop_eta_async: {e}")
        return None

@app.route('/get_share_bus_stop')
@async_route
async def get_share_bus_stop():
    """Get shared bus stop with caching."""
    start_time = time.time()

    # Extract parameters
    seq = request.args.get('s')
    route_param = request.args.get('r')
    bound_param = request.args.get('b')

    # Generate cache key
    cache_key = cache_manager._generate_key('share_stop', seq, route_param, bound_param)

    # Check cache
    cached_result = get_cached_stops(cache_key)
    if cached_result:
        logger.info(f"Share bus stop served from cache in {time.time() - start_time:.3f}s")
        return cached_result if cached_result else ''

    # Load fresh data
    bus_stop = await load_share_bus_stop_async(seq, route_param, bound_param)

    # Cache result
    if bus_stop is not None:
        cache_stops(cache_key, bus_stop)

    logger.info(f"Share bus stop loaded in {time.time() - start_time:.3f}s")
    return bus_stop if bus_stop is not None else ''

async def load_share_bus_stop_async(seq, route_param, bound_param):
    """Load shared bus stop asynchronously."""
    global route, bound
    route = route_param
    bound = bound_param

    boundtype = 'inbound' if bound_param == 'I' else 'outbound'

    try:
        dest_en = await check_kmb_route_async(route_param, boundtype)
        if dest_en is not None:
            boundtype = await check_ctb_route_async(dest_en, route_param)

        if boundtype is not None:
            routestop_data = await fetch_ctb_route_stops(route_param, boundtype, proxies)

            if routestop_data:
                for row in routestop_data:
                    if str(row["seq"]) == seq:
                        return row["stop"]
            else:
                logger.error("Error loading CTB bus routestop data")
                return None
        else:
            return None

    except Exception as e:
        logger.error(f"Error in load_share_bus_stop_async: {e}")
        return None

async def check_kmb_route_async(route_param, boundtype):
    """Check KMB route asynchronously."""
    try:
        client = await get_async_client(proxies)
        url = f"https://data.etabus.gov.hk/v1/transport/kmb/route/{route_param}/{boundtype}/1"
        result = await client.fetch_json(url)

        if result and result.get('data') and isinstance(result['data'], dict):
            return result['data'].get('dest_en')
        else:
            logger.warning("Invalid KMB route response format")
            return None

    except Exception as e:
        logger.error(f"Error checking KMB route: {e}")
        return None

async def check_ctb_route_async(dest_en, route_param):
    """Check CTB route asynchronously."""
    try:
        client = await get_async_client(proxies)
        url = f"https://rt.data.gov.hk/v2/transport/citybus/route/CTB/{route_param}"
        result = await client.fetch_json(url)

        if result and result.get('data') and isinstance(result['data'], dict):
            if not result['data']:
                return None

            dest_en_cmb = result['data'].get('dest_en')
            if dest_en_cmb and (dest_en in dest_en_cmb or dest_en_cmb in dest_en):
                return "outbound"
            else:
                return "inbound"
        else:
            logger.warning("Invalid CTB route response format")
            return None

    except Exception as e:
        logger.error(f"Error checking CTB route: {e}")
        return None

@app.route('/cache_stats')
def cache_stats():
    """Get cache statistics for monitoring."""
    return jsonify(cache_manager.get_cache_stats())

# Initialize CTB data at startup
load_ctb_data_at_startup()

def cleanup_async_resources():
    """Cleanup async resources properly."""
    global _global_loop, _loop_thread

    try:
        # Close the global HTTP client
        if _global_loop and not _global_loop.is_closed():
            future = asyncio.run_coroutine_threadsafe(close_global_client(), _global_loop)
            future.result(timeout=5)

            # Stop the event loop
            _global_loop.call_soon_threadsafe(_global_loop.stop)

        # Wait for thread to finish
        if _loop_thread and _loop_thread.is_alive():
            _loop_thread.join(timeout=5)

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

@app.teardown_appcontext
def cleanup(error):
    """Cleanup async resources."""
    # Note: This is called per request, so we don't cleanup global resources here
    pass

if __name__ == '__main__':
    import atexit
    atexit.register(cleanup_async_resources)

    try:
        app.run(debug=True)
    finally:
        cleanup_async_resources()
