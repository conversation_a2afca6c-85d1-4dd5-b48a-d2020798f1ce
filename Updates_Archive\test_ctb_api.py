#!/usr/bin/env python3
"""
Test script to verify CTB API integration and compare with old static data.
"""

import requests
import json
import time

def test_ctb_api():
    """Test the CTB API endpoint."""
    print("🧪 Testing CTB API Integration")
    print("=" * 50)

    # Test the new CTB API
    print("\n1. Testing CTB API endpoint...")
    try:
        url = "https://rt.data.gov.hk/v2/transport/citybus/route/ctb"
        print(f"   Fetching: {url}")

        start_time = time.time()
        response = requests.get(url, timeout=10)
        end_time = time.time()

        print(f"   Response time: {end_time - start_time:.3f} seconds")
        print(f"   Status code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()

            if 'data' in data:
                routes = data['data']
                print(f"   ✅ Success! Found {len(routes)} CTB routes")

                # Show sample route data
                if routes:
                    sample_route = routes[0]
                    print(f"\n   📋 Sample route data:")
                    for key, value in sample_route.items():
                        print(f"      {key}: {value}")

                return routes
            else:
                print(f"   ❌ No 'data' field in response")
                print(f"   Response keys: {list(data.keys())}")
                return None
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return None

    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def analyze_api_data(new_routes):
    """Analyze the API data structure."""
    print("\n2. Analyzing API data structure...")

    if new_routes:
        print(f"   📊 API Data Analysis:")
        print(f"   Total routes: {len(new_routes)}")

        # Analyze data structure
        sample_route = new_routes[0]
        print(f"   Fields: {sorted(sample_route.keys())}")

        # Analyze service types
        service_types = set()
        route_numbers = set()

        for route in new_routes:
            if 'service_type' in route:
                service_types.add(route['service_type'])
            if 'route' in route:
                route_numbers.add(route['route'])

        print(f"   Service types found: {sorted(service_types)}")
        print(f"   Sample routes: {sorted(list(route_numbers))[:10]}{'...' if len(route_numbers) > 10 else ''}")

        # Check for required fields
        required_fields = ['route', 'orig_tc', 'dest_tc']
        missing_fields = []

        for field in required_fields:
            if field not in sample_route:
                missing_fields.append(field)

        if missing_fields:
            print(f"   ⚠️  Missing expected fields: {missing_fields}")
        else:
            print(f"   ✅ All required fields present")
    else:
        print("   ❌ No data to analyze")

def test_app_integration():
    """Test the integration with the Flask app."""
    print("\n3. Testing Flask app integration...")

    try:
        # Test if app is running
        response = requests.get('http://localhost:5000/cache_stats', timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ App is running")
            print(f"   CTB routes count: {stats.get('ctb_routes_count', 'N/A')}")
            print(f"   CTB cache age: {stats.get('ctb_cache_age_hours', 'N/A')} hours")
        else:
            print(f"   ❌ App returned HTTP {response.status_code}")

    except requests.exceptions.ConnectionError:
        print("   ⚠️  App not running. Start with: python app_fixed.py")
    except Exception as e:
        print(f"   ❌ Error testing app: {e}")

def test_refresh_endpoint():
    """Test the CTB data refresh endpoint."""
    print("\n4. Testing CTB refresh endpoint...")

    try:
        response = requests.get('http://localhost:5000/refresh_ctb_data', timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Refresh successful")
            print(f"   Routes count: {result.get('routes_count')}")
            print(f"   Refresh time: {result.get('refresh_time_seconds')} seconds")
        else:
            print(f"   ❌ Refresh failed: HTTP {response.status_code}")
            print(f"   Response: {response.text}")

    except requests.exceptions.ConnectionError:
        print("   ⚠️  App not running. Start with: python app_fixed.py")
    except Exception as e:
        print(f"   ❌ Error testing refresh: {e}")

def save_sample_data(routes):
    """Save sample of new data for inspection."""
    if routes:
        sample_data = routes[:5]  # First 5 routes
        try:
            with open('ctb_api_sample.json', 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, ensure_ascii=False, indent=2)
            print(f"\n   💾 Saved sample data to ctb_api_sample.json")
        except Exception as e:
            print(f"   ❌ Could not save sample: {e}")

def main():
    """Main test function."""
    print("🚌 CTB API Integration Test")
    print("Testing the new dynamic CTB route fetching...")

    # Test 1: CTB API
    new_routes = test_ctb_api()

    # Test 2: Analyze API data
    if new_routes:
        analyze_api_data(new_routes)
        save_sample_data(new_routes)

    # Test 3: App integration
    test_app_integration()

    # Test 4: Refresh endpoint
    test_refresh_endpoint()

    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("✅ CTB API is working" if new_routes else "❌ CTB API failed")
    print("✅ Ready to use app_fixed.py with dynamic CTB data!")

    print("\n📋 Next steps:")
    print("1. Run: python app_fixed.py")
    print("2. Visit: http://localhost:5000/cache_stats")
    print("3. Visit: http://localhost:5000/refresh_ctb_data")
    print("4. Test route endpoints as usual")

if __name__ == "__main__":
    main()
