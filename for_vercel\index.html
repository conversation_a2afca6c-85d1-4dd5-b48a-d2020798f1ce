<!DOCTYPE html>
<html>
	<head>
		<meta name="viewport" content="width=device-width, initial-scale=1.0" charset="UTF-8">
		<title>BusTime</title>
		<!-- Favicon to prevent 404 error -->
		<link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚌</text></svg>">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"/>
		<style>
		body, html {
			height: 100%;
			display: flex;
			align-items: center;
			flex-direction: column;
		}
		.list-group-item span {
			margin-right: 20px;
		}
		.btn-fixed-width {
			width: 70px;
			margin-right: 20px;
		}
		#route-input {
			width: 300px;
			margin-top: 20px;
		}
		#btn-back-to-top {
			position: fixed;
			bottom: 20px;
			right: 10px;
			z-index: 99;
			color: #C0C0C0;
			display: none;
		}
		.inline-container {
			display: inline-flex;
			align-items: center;
		}
		#bookmark-add-btn {
			display: none;
		}
		#stop-info {
			white-space: pre-line;
			padding-left: 12px;
		}
		#bm-table td {
			vertical-align: middle;
			padding: 0.1rem;
		}
		.input-container {
            align-items: center;
        }

		.route-badge {
			color: white;
			padding: 3px 8px;
			border-radius: 3px;
			font-size: 0.9em;
			font-weight: 600;
			margin-right: 5px;
		}

		/* KMB Style - Red */
		.route-badge.kmb {
			background: #d32f2f;  /* KMB Red */
		}

		/* CTB Style - Yellow */
		.route-badge.ctb {
			background: #ffeb3b;  /* Bright Light Yellow */
			color: #000;          /* Black text for better contrast on yellow */
		}

		/* GMB Style - Green */
		.route-badge.gmb {
			background: #4caf50;  /* Green */
			color: white;         /* White text for good contrast */
		}

		.route-connector {
			color: #666;
			font-size: 0.9em;
		}

		.route-destination {
			font-weight: 600;
		}

		.special-route {
			color: #28a745;
			font-weight: bold;
		}

		/* Custom Button Colors */
		.btn-kmb {
			background-color: #d32f2f;  /* KMB Red */
			border-color: #d32f2f;
			color: white;
		}

		.btn-kmb:hover {
			background-color: #b71c1c;  /* Darker red on hover */
			border-color: #b71c1c;
			color: white;
		}

		.btn-ctb {
			background-color: #ffeb3b;  /* Bright Light Yellow */
			border-color: #ffeb3b;
			color: #000;                /* Black text for better contrast */
		}

		.btn-ctb:hover {
			background-color: #fdd835;  /* Slightly darker yellow on hover */
			border-color: #fdd835;
			color: #000;
		}

		.btn-gmb {
			background-color: #4caf50;  /* GMB Green */
			border-color: #4caf50;
			color: white;
		}

		.btn-gmb:hover {
			background-color: #388e3c;  /* Darker green on hover */
			border-color: #388e3c;
			color: white;
		}
		</style>
	</head>
	<body>
		<button type="button" class="btn btn-floating btn-lg" id="btn-back-to-top">
			<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-arrow-up-circle" viewBox="0 0 16 16">
				<path fill-rule="evenodd" d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8m15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0m-7.5 3.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708l3-3a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707z"/>
			</svg>
		</button>
		<div class="input-container">
			<input type="text" id="route-input" placeholder="輸入巴士號碼" />
			<!-- <input type="checkbox" value="" id="serviceTypeCheck"> -->
			<!-- <label class="form-check-label" for="serviceTypeCheck">特別班次</label> -->
		</div>
		<p></p>
		<div id="bookmark-container"></div>
		<div class="inline-container">
			<!-- Left arrow for previous stop -->
			<button type="button" id="prev-stop-btn" class="btn" style="display: none; margin-right: 10px;">
				<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="#6c757d" class="bi bi-arrow-left-circle" viewBox="0 0 16 16">
					<path fill-rule="evenodd" d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8m15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0m-4.5-.5a.5.5 0 0 1 0 1H5.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L5.707 7.5z"/>
				</svg>
			</button>
			<span id="stop-info"></span>
			<button type="button" id="bookmark-add-btn" class="btn">
				<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="#6c757d" class="bi bi-bookmark-star" viewBox="0 0 16 16">
					<path d="M7.84 4.1a.178.178 0 0 1 .32 0l.634 1.285a.18.18 0 0 0 .134.098l1.42.206c.145.021.204.2.098.303L9.42 6.993a.18.18 0 0 0-.051.158l.242 1.414a.178.178 0 0 1-.258.187l-1.27-.668a.18.18 0 0 0-.165 0l-1.27.668a.178.178 0 0 1-.257-.187l.242-1.414a.18.18 0 0 0-.05-.158l-1.03-1.001a.178.178 0 0 1 .098-.303l1.42-.206a.18.18 0 0 0 .134-.098z"/>
					<path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1z"/>
				</svg>
			</button>
			<!-- Right arrow for next stop -->
			<button type="button" id="next-stop-btn" class="btn" style="display: none; margin-left: 10px;">
				<svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="#6c757d" class="bi bi-arrow-right-circle" viewBox="0 0 16 16">
					<path fill-rule="evenodd" d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8m15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0M4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5z"/>
				</svg>
			</button>
		</div>
		<div id="eta-table-container"></div>
		<span id="eta-info"></span>
		<p></p>
		<ul id="stops-list" class="list-group"></ul>
		<p></p>
		<ul id="routes-list" class="list-group"></ul>
		<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
		<script>
			document.addEventListener("DOMContentLoaded", function () {
				let fetchedData = [];
				let intervalID;
				const rerunInterval = 60000;
				const sharedRoutes = new Set();

				// Global variables for stop navigation
				let currentRouteStops = [];
				let currentStopIndex = -1;
				
				let currentCompany = null;
				let currentRoute = '';
				let currentRouteId = '';
				let currentBound = '';
				let currentDestTc = '';

				const fetchData = () => {
					// Try main endpoint first, fallback to quick routes if it fails
					return fetch("/get_bus_routes", {
						method: 'GET',
						headers: {
							'Accept': 'application/json',
							'Content-Type': 'application/json'
						}
					})
						.then((response) => {
							if (!response.ok) {
								throw new Error(`HTTP ${response.status}: ${response.statusText}`);
							}
							return response.text();
						})
						.then((text) => {
							try {
								const data = JSON.parse(text);
								console.log("Successfully loaded routes:", data.length);
								fetchedData = data;
								return data;
							} catch (e) {
								console.error("JSON Parse Error:", e);
								console.error("Response text:", text.substring(0, 200));
								throw new Error("Invalid JSON response from server");
							}
						})
						.catch((error) => {
							console.error("Route loading failed:", error);
							// Show user-friendly error message
							const routesList = document.getElementById("routes-list");
							if (routesList) {
								routesList.innerHTML = `
									<div class="alert alert-danger" role="alert">
										<h5>載入失敗</h5>
										<p>無法載入巴士路線資料，請稍後再試...</p>
										<button class="btn btn-primary" onclick="location.reload()">重新載入</button>
									</div>
								`;
							}
							return [];
						});
				};

				// Display the initial data
				const displayData = (dataArray) => {
					const routesList = document.getElementById("routes-list");
					routesList.innerHTML = "";
					if (dataArray && Array.isArray(dataArray)) {
						const kmbRoutes = [];

						// Process KMB routes
						dataArray.forEach((route) => {
							if (route.co === "KMB" && route.service_type == 1) {
								var kmburl = "/get_kmb_bus_routestop?r=" +	encodeURIComponent(route.route) + "&s=" + encodeURIComponent(route.service_type);
								routesList.appendChild(loadroutestop(route, kmburl, route.bound));
								kmbRoutes.push(route);
							}
						});

						// Process CTB routes
						dataArray.forEach((route) => {
							if (route.co === "CTB") {
								const kmbRoute = kmbRoutes.find(r => r.route === route.route);
								if (kmbRoute && (isTextSimilar(route.orig_en, kmbRoute.orig_en) || isTextSimilar(route.orig_en, kmbRoute.dest_en) || isTextSimilar(route.dest_en, kmbRoute.orig_en) || isTextSimilar(route.dest_en, kmbRoute.dest_en))) {
									sharedRoutes.add(route.route);
								} else {
									var ctburl = "/get_ctb_bus_routestop?r=" +	encodeURIComponent(route.route);
									routesList.appendChild(loadroutestop(route, ctburl, "I"));
									routesList.appendChild(loadroutestop(route, ctburl, "O"));
								}
							}
						});

						// Process GMB routes
						dataArray.forEach((route) => {
							if (route.co === "GMB") {
								var gmburl = "/get_gmb_bus_routestop?r=" + encodeURIComponent(route.route_id) +
								            "&b=" + encodeURIComponent(route.route_seq);

								const bound = route.bound || "O"; // Default to outbound if not specified
								routesList.appendChild(loadroutestop(route, gmburl, bound));
							}
						});

					} else {
						const errorMessage = document.createElement("p");
						errorMessage.textContent = "Error fetching data. Please try again.";
						routesList.appendChild(errorMessage);
					}
				};

				// Function to check text similarity
				const isTextSimilar = (text1, text2) => {
					if (!text1 || !text2) return false;
					const words1 = text1.toLowerCase().split(/\s+/);
					const words2 = text2.toLowerCase().split(/\s+/);
					return words1.some(word => words2.includes(word)) || words2.some(word => words1.includes(word));
				};

				function loadroutestop(route, url, bound) {
					const listItem = document.createElement("li");
					listItem.classList.add("list-group-item");
					const button = document.createElement("button");

					// Set button style based on company
					if (route.co === "KMB") {
						button.classList.add("btn", "btn-kmb", "btn-fixed-width");  // KMB Red
					} else if (route.co === "GMB") {
						button.classList.add("btn", "btn-gmb", "btn-fixed-width");  // GMB Green
					} else {
						button.classList.add("btn", "btn-ctb", "btn-fixed-width");  // CTB Yellow
					}

					button.textContent = route.route;

					let orig_tc = route.orig_tc;
					let dest_tc = route.dest_tc;
					if (route.co === "CTB" && bound === "I") {
						orig_tc = route.dest_tc;
						dest_tc = route.orig_tc;
					} else if (route.co === "GMB" && route.region) {
						// Map English region codes to Chinese names
						const regionMap = {
							'HKI': '港島',
							'KLN': '九龍',
							'NT': '新界'
						};
						const chineseRegion = regionMap[route.region] || route.region;
						const isSpecialRoute = route.description_tc.includes("特別") ? '<span class="special-route">[特別班次]</span>' : '';
						orig_tc = `${chineseRegion} - ${route.orig_tc} ${isSpecialRoute}`;
					}

					const orig_dest_tc = document.createElement("span");
					orig_dest_tc.innerHTML = '<small>' + orig_tc + '</small><br><span class="route-connector">往</span><strong>' + dest_tc + '</strong>';
					orig_dest_tc.style.display = "inline-block";
					orig_dest_tc.style.verticalAlign = "middle";

					// Add region parameter for GMB routes
					if (route.co === "GMB" && route.region) {
						url += "&region=" + encodeURIComponent(route.region);
					}
					url += "&b=" + encodeURIComponent(bound);

					button.onclick = function () {
						loadstoplist(url, route.route, dest_tc, bound, route.co, route.region, route.route_id);
					};
					listItem.appendChild(button);
					listItem.appendChild(orig_dest_tc);
					return listItem;
				}

				async function loadstoplist(url, route, dest_tc, bound, company = null, region = null, route_id = null) {
					const coords = await getCurrentPosition();
					if (coords) {
						url += "&lat=" + encodeURIComponent(coords.latitude) + "&lon=" + encodeURIComponent(coords.longitude);
					}
					$.ajax({
						url: url,
						type: "GET",
						success: function (response) {
							const stopsList = document.getElementById("stops-list");
							stopsList.innerHTML = "";
							const routeHeader = document.createElement("li");
							routeHeader.classList.add("list-group-item");
							const routeInfo = document.createElement("span");
							// Determine badge class based on company
							let badgeClass = 'route-badge';
							if (company === 'KMB') {
								badgeClass += ' kmb';  // KMB Red
							} else if (company === 'GMB') {
								badgeClass += ' gmb';  // GMB Green
							} else if (company === 'CTB') {
								badgeClass += ' ctb';  // CTB Yellow
							}
							routeInfo.innerHTML = '<span class="' + badgeClass + '">' + route + '</span><span class="route-connector">往</span><span class="route-destination">' + dest_tc + '</span>';
							routeHeader.appendChild(routeInfo);
							stopsList.appendChild(routeHeader);
							stopRerunRequest();
							document.getElementById("stop-info").innerHTML = "";
							document.getElementById("bookmark-add-btn").style.display = "none";
							document.getElementById("eta-table-container").innerHTML = "";
							document.getElementById("eta-info").innerHTML = "";

							// Store route stops data for navigation
							currentRouteStops = response;
							currentStopIndex = -1;

							currentCompany = company;
							currentRoute = route;
							currentRouteId = route_id;
							currentBound = bound;
							currentDestTc = dest_tc;

							// Debug logging for GMB routes
							console.log(company, "Route Loaded:", {
								route: route,
								route_id: route_id,
								bound: bound,
								dest_tc: dest_tc,
								region: region,
							});

							// Hide navigation arrows initially
							document.getElementById("prev-stop-btn").style.display = "none";
							document.getElementById("next-stop-btn").style.display = "none";

							var stopCnt = 0;
							var closestStopId = null;
							if (coords && response && response.length > 0) {
								let dist_list = response.slice().sort((a, b) => {
									// Add safety check for array elements
									if (!a || !b || !a[3] || !b[3]) return 0;
									var distA = parseFloat(a[3]);
									var distB = parseFloat(b[3]);
									return distA - distB;
								});

								// Check if dist_list has elements and first element is valid
								if (dist_list.length > 0 && dist_list[0] && dist_list[0][0]) {
									const closestStopObj = dist_list[0];
									// console.log(closestStopObj);
									closestStopId = closestStopObj[0];
								}
							}
							// Add safety check for response array
							if (response && Array.isArray(response)) {
								response.forEach((stop_obj) => {
									// Add safety check for stop object
									if (!stop_obj || !Array.isArray(stop_obj) || stop_obj.length < 3) {
										console.warn("Invalid stop object:", stop_obj);
										return; // Skip this iteration
									}

									const listStop = document.createElement("li");
									listStop.classList.add("list-group-item");
									const stopIndex = document.createElement("span");
									const link = document.createElement("a");
									stopIndex.textContent = String(stopCnt).padStart(2, "0") + ". ";
									stopCnt++;
									if (stop_obj[0] == closestStopId) {
										listStop.style.backgroundColor = "#ffdf80";
									}
									link.id = stop_obj[0];
									link.textContent = stop_obj[1] || "Unknown Stop";
									link.href = "#";
									link.onclick = function () {
										// Find the index of this stop in the route
										currentStopIndex = currentRouteStops.findIndex(stop => stop[0] === stop_obj[0]);

										setCookies(route, dest_tc, stop_obj[1], stop_obj[0], stop_obj[2], bound, route_id, company);
										loadStopInfo(route, dest_tc, stop_obj[1], company);
										loadstopeta(stop_obj[0], stop_obj[2], route, bound, route_id, company);
										stopRerunRequest();
										intervalID = setInterval(rerunRequest, rerunInterval);
										console.log("🔄 Auto-refresh started for stop:", stop_obj[1], "- Interval ID:", intervalID);

										// Update navigation arrows
										updateNavigationArrows();
									};
									listStop.appendChild(stopIndex);
									listStop.appendChild(link);
									stopsList.appendChild(listStop);
								});
							}

							if (closestStopId !== null) {
								document.getElementById(closestStopId).focus();
							} else {
								backToTop();
							}

							console.log("Stop List", response);
						},
						error: function (error) {
							console.log(error);
						},
					});
				}

				async function loadstopeta(stop_id, seq, route, bound, route_id = null, company = null, forceRefresh = false) {
					console.log("Stop Info", {
								company: company,
								route: route,
								route_id: route_id,
								bound: bound,
								stop_id: stop_id,
								seq: seq,		
							});
					const tableContainer = document.getElementById("eta-table-container");
					tableContainer.innerHTML = "";
					document.getElementById("eta-info").innerHTML = "";
					const table = document.createElement("table");
					const tbody = document.createElement("tbody");
					table.classList.add("table", "table-striped");
					const headerRow = document.createElement("tr");
					const headerTitle = ["班次", "預計到達時間", "備註"];
					for (const key in headerTitle) {
						const headerCell = document.createElement("th");
						headerCell.textContent = headerTitle[key];
						headerRow.appendChild(headerCell);
					}
					table.appendChild(headerRow);
					let eta_list = [];

					// Add force_refresh parameter for real-time updates
					const forceParam = forceRefresh ? "&force_refresh=true" : "";
					if (forceRefresh) {
						console.log("🔄 Force refresh enabled - bypassing ETA cache for real-time data");
					}

					try {
						if (company === "GMB") {
							console.log("🚌 GMB ETA Request:", {
								stop_id: stop_id,
								route_id: route_id,
								stop_seq: seq,
							});
							const response = await $.ajax({
								url: "/get_gmb_stop_eta?id=" + encodeURIComponent(stop_id) + "&r=" + encodeURIComponent(route_id) + "&s=" + encodeURIComponent(seq) + forceParam,
								type: "GET",
							});
							if (response.length !== 0) {
								response.forEach((eta_obj) => {
									eta_list.push(eta_obj);
								});
							}

						} else if (sharedRoutes.has(route)) {
							console.log("🚌 Shared Routes ETA Request:", {
								stop_id: stop_id,
								route: route,
							});
							const response = await $.ajax({
								url: "/get_kmb_stop_eta?id=" + encodeURIComponent(stop_id) + "&r=" + encodeURIComponent(route) + "&b=" + encodeURIComponent(bound) + forceParam,
								type: "GET",
							});
							if (response.length !== 0) {
								response.forEach((eta_obj) => {
									eta_list.push(eta_obj);
								});
							}
							const stopIdResponse = await $.ajax({
								url: "/get_share_bus_stop?s=" + encodeURIComponent(seq) + "&r=" + encodeURIComponent(route) + "&b=" + encodeURIComponent(bound),
								type: "GET",
							});
							if (stopIdResponse.length !== 0) {
								const ctbResponse = await $.ajax({
									url: "/get_ctb_stop_eta?id=" + encodeURIComponent(stopIdResponse) + "&r=" + encodeURIComponent(route) + "&b=" + encodeURIComponent(bound) + forceParam,
									type: "GET",
								});
								if (ctbResponse.length !== 0) {
									ctbResponse.forEach((eta_obj) => {
										eta_list.push(eta_obj);
									});
								}
							}

						} else if (company === "CTB") {
							console.log("🚌 CTB ETA Request:", {
								stop_id: stop_id,
								route: route,
							});
							const response = await $.ajax({
								url: "/get_ctb_stop_eta?id=" + encodeURIComponent(stop_id) + "&r=" + encodeURIComponent(route) + "&b=" + encodeURIComponent(bound) + forceParam,
								type: "GET",
							});
							if (response.length !== 0) {
								response.forEach((eta_obj) => {
									eta_list.push(eta_obj);
								});
							}

						} else {
							console.log("🚌 KMB ETA Request:", {
								stop_id: stop_id,
								route: route,
							});
							const response = await $.ajax({
								url: "/get_kmb_stop_eta?id=" + encodeURIComponent(stop_id) + "&r=" + encodeURIComponent(route) + "&b=" + encodeURIComponent(bound) + forceParam,
								type: "GET",
							});
							if (response.length !== 0) {
								response.forEach((eta_obj) => {
									eta_list.push(eta_obj);
								});
							}
						}
						eta_list.sort(function (a, b) {
							var dateA = new Date(a[0]);
							var dateB = new Date(b[0]);
							return dateA - dateB;
						});
						console.log(eta_list);
						var timeUpdate = true;
						var seqCnt = 1;
						var etaFound = false;
						eta_list.forEach((eta_obj) => {
							if (eta_obj[0] !== "" && eta_obj[0] !== null) {
								const row = document.createElement("tr");
								const cell = document.createElement("td");
								cell.textContent = seqCnt;
								row.appendChild(cell);
								seqCnt++;
								for (let i = 1; i < eta_obj.length - 1; i++) {
									const cell = document.createElement("td");
									cell.textContent = eta_obj[i];
									row.appendChild(cell);
								}
								tbody.appendChild(row);
								etaFound = true;
							} else if (eta_list.length === 1 && eta_obj[2] !== "") {
								const row = document.createElement("tr");
								const [cell1, cell2, cell3] = Array.from({ length: 3 },	() => document.createElement("td"));
								[cell1.textContent, cell2.textContent, cell3.textContent] = ["", "", eta_obj[2]];
								row.append(cell1, cell2, cell3);
								tbody.appendChild(row);
								etaFound = true;
							}
							if (timeUpdate) {
								document.getElementById("eta-info").textContent = "最後更新: " + eta_obj[3];
								timeUpdate = false;
							}
						});
						if (!etaFound && eta_list.length <= 1) {
							const row = document.createElement("tr");
							const [cell1, cell2, cell3] = Array.from({ length: 3 },	() => document.createElement("td"));
							[cell1.textContent, cell2.textContent, cell3.textContent] = ["", "", "暫沒班次"];
							row.append(cell1, cell2, cell3);
							tbody.appendChild(row);
							if (timeUpdate) {
								document.getElementById("eta-info").textContent = "最後更新: " + getCurrentDateTime();
								timeUpdate = false;
							}
						}
						table.appendChild(tbody);
						tableContainer.appendChild(table);
					} catch (error) {
						console.error("Error loading table:", error);
					}
				}

				// Show loading indicator
				const routesList = document.getElementById("routes-list");
				if (routesList) {
					routesList.innerHTML = `
						<div class="text-center p-4">
							<div class="spinner-border text-primary" role="status">
								<span class="sr-only">載入中...</span>
							</div>
							<p class="mt-2">正在載入巴士路線資料...</p>
							<small class="text-muted">首次載入可能需要較長時間</small>
						</div>
					`;
				}

				// Fetch and display the data
				fetchData()
				.then((data) => {
					displayData(data);

					// After successful load, trigger background warmup for CTB routes
					if (data && data.length > 0) {
						console.log("Triggering background warmup for CTB routes (GMB temporarily disabled)...");
						fetch("/warmup_background")
							.then(response => response.json())
							.then(result => {
								console.log("Background warmup triggered:", result);
							})
							.catch(error => {
								console.log("Background warmup failed:", error);
							});
					}
				})
				.catch((error) => {
					console.error("Error fetching data:", error);
					displayData(null);
				});

				// Filter the data based on the input value
				const filterData = () => {
					stopRerunRequest();
					document.getElementById("stop-info").innerHTML = "";
					document.getElementById("bookmark-add-btn").style.display = "none";
					document.getElementById("eta-table-container").innerHTML = "";
					document.getElementById("eta-info").innerHTML = "";
					document.getElementById("stops-list").innerHTML = "";
					document.getElementById("prev-stop-btn").style.display = "none";
					document.getElementById("next-stop-btn").style.display = "none";
					const input = document.getElementById("route-input");
					const filterValue = input.value.toLowerCase();
					const filteredData = fetchedData.filter((route) => {
						return route.route.toLowerCase().startsWith(filterValue);
					});

					displayData(filteredData);
				};

				loadBookmarkTable(getAllBookmarks());

				// Event listener for input changes
				const input = document.getElementById("route-input");
				input.addEventListener("input", filterData);

				// Back-to-top button
				const topBtn = document.getElementById("btn-back-to-top");
				window.onscroll = function () {
					scrollFunction();
				};

				function scrollFunction() {
					if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
						topBtn.style.display = "block";
					} else {
						topBtn.style.display = "none";
					}
				}

				topBtn.addEventListener("click", backToTop);

				function backToTop() {
					document.body.scrollTop = 0;
					document.documentElement.scrollTop = 0;
				}

				function rerunRequest() {
					const stop_id = getCookie("stop_id");
					const seq = getCookie("seq");
					const route = getCookie("route");
					const bound = getCookie("bound");
					const route_id = getCookie("route_id");
					const company = getCookie("company");
					loadstopeta(stop_id, seq, route, bound, route_id, company);
				}

				function stopRerunRequest() {
					if (intervalID) {
						clearInterval(intervalID);
						console.log("⏹️ Auto-refresh stopped");
						intervalID = null;
					}
				}

				function setCookies(route, dest_tc, stop_name, stop_id, seq, bound, route_id, company) {
					setCookie("route", route);
					setCookie("dest_tc", dest_tc);
					setCookie("stop_name", stop_name);
					setCookie("stop_id", stop_id);
					setCookie("seq", seq);
					setCookie("bound", bound);			
					setCookie("route_id", route_id);
					setCookie("company", company);
				}

				function setCookie(cookieName, cookieValue) {
					const expires = new Date();
					expires.setTime(expires.getTime() + (24 * 60 * 60 * 1000)); // 24 hours
					document.cookie = cookieName + "=" + encodeURIComponent(cookieValue) +
						"; expires=" + expires.toUTCString() +
						"; path=/; SameSite=Lax";
				}

				function getCookie(cookieName) {
					const name = cookieName + "=";
					const decodedCookie = decodeURIComponent(document.cookie);
					const cookieArray = decodedCookie.split(";");
					for (let i = 0; i < cookieArray.length; i++) {
						let cookie = cookieArray[i];
						while (cookie.charAt(0) === " ") {
							cookie = cookie.substring(1);
						}
						if (cookie.indexOf(name) === 0) {
							return cookie.substring(name.length, cookie.length);
						}
					}
					return "";
				}

				function loadStopInfo(route, dest_tc, stop_name, company = null) {
					const stopInfoElement = document.getElementById("stop-info");

					// Determine badge class based on company
					let badgeClass = 'route-badge';
					if (company === 'KMB') {
						badgeClass += ' kmb';  // KMB Red
					} else if (company === 'GMB') {
						badgeClass += ' gmb';  // GMB Green
					} else if (company === 'CTB') {
						badgeClass += ' ctb';  // CTB Yellow
					} else {
						// For bookmarks without company info, detect from stop_id pattern
						const stop_id = getCookie("stop_id");
						if (stop_id && stop_id.length <= 6) {
							badgeClass += ' ctb';  // CTB routes have 6 digit stop IDs
						} else if (stop_id && stop_id.length <= 8) {
							badgeClass += ' gmb';  // GMB routes have 8 digit stop IDs
						} else {
							badgeClass += ' kmb';  // KMB routes have longest stop IDs
						}
					}

					stopInfoElement.innerHTML = '<span class="' + badgeClass + '">' + route + '</span><span class="route-connector">往 </span><span class="route-destination">' + dest_tc + '</span><br><strong>' + stop_name + '</strong>';
					document.getElementById("bookmark-add-btn").style.display = "block";
				}

				const addBtn = document.getElementById("bookmark-add-btn");
				addBtn.addEventListener("click", addBookmark);

				function addBookmark() {
					const route = getCookie("route");
					const dest_tc = getCookie("dest_tc");
					const stop_name = getCookie("stop_name");
					const stop_id = getCookie("stop_id");
					const seq = getCookie("seq");
					const bound = getCookie("bound");
					const route_id = getCookie("route_id");
					const company = getCookie("company");
					let count = localStorage.getItem('bookmarkCount');
  					count = count ? parseInt(count) : 0;
					const bookmarkName = `bookmark${count + 1}`;
  					localStorage.setItem(bookmarkName, JSON.stringify([bookmarkName, route, dest_tc, stop_name, stop_id, seq, bound, route_id, company]));
					localStorage.setItem('bookmarkCount', count + 1);
					loadBookmarkTable(getAllBookmarks());
				}

				function getAllBookmarks() {
					const bookmarkCount = parseInt(localStorage.getItem('bookmarkCount')) || 0;
					const bookmarks = [];
					for (let i = 1; i <= bookmarkCount; i++) {
						const bookmarkName = `bookmark${i}`;
						const bookmark = JSON.parse(localStorage.getItem(bookmarkName));
						if (bookmark) {
							bookmarks.push(bookmark);
						}
					}
					return bookmarks;
				}

				function loadBookmarkTable(bookmarks) {
					const bookmarkCount = parseInt(localStorage.getItem('bookmarkCount')) || 0;
					const tableContainer = document.getElementById("bookmark-container");
					tableContainer.innerHTML = "";
					const table = document.createElement("table");
					table.setAttribute("id", "bm-table");
					const tbody = document.createElement("tbody");
					table.classList.add("table");
					bookmarks.forEach((bookmark) => {
						const row = document.createElement("tr");
						const removeCell = document.createElement("td");
    					const removeButton = document.createElement("button");
						removeButton.classList.add("btn");
						const removeIcon = document.createElementNS("http://www.w3.org/2000/svg", "svg");
						removeIcon.setAttribute("xmlns", "http://www.w3.org/2000/svg");
						removeIcon.setAttribute("width", "30");
						removeIcon.setAttribute("height", "30");
						removeIcon.setAttribute("fill", "#6c757d");
						removeIcon.setAttribute("class", "bi bi-bookmark-x");
						removeIcon.setAttribute("viewBox", "0 0 16 16");
						removeIcon.innerHTML = "<<path fill-rule='evenodd' d='M6.146 5.146a.5.5 0 0 1 .708 0L8 6.293l1.146-1.147a.5.5 0 1 1 .708.708L8.707 7l1.147 1.146a.5.5 0 0 1-.708.708L8 7.707 6.854 8.854a.5.5 0 1 1-.708-.708L7.293 7 6.146 5.854a.5.5 0 0 1 0-.708'/><path d='M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1z'/>";
						removeButton.appendChild(removeIcon);
						removeButton.addEventListener("click", function () {
							removeBookmark(bookmark[0]);
							loadBookmarkTable(getAllBookmarks());
						});
						removeCell.appendChild(removeButton);
						row.appendChild(removeCell);
						for (let i = 1; i < 3; i++) {
							const cell = document.createElement("td");

							if (i === 1) {
								// Apply badge styling to route number (bookmark[1])
								const route = bookmark[i];
								const company = bookmark[8];
								
								// Determine badge class based on company
								let badgeClass = 'route-badge';
								if (company === 'KMB') {
									badgeClass += ' kmb';  // KMB Red
								} else if (company === 'GMB') {
									badgeClass += ' gmb';  // GMB Green
								} else if (company === 'CTB') {
									badgeClass += ' ctb';  // CTB Yellow
								} else {
									// For bookmarks without company info, detect from stop_id pattern
									const stop_id = bookmark[4];
									if (stop_id && stop_id.length <= 6) {
										badgeClass += ' ctb';  // CTB routes have 6 digit stop IDs
									} else if (stop_id && stop_id.length <= 8) {
										badgeClass += ' gmb';  // GMB routes have 8 digit stop IDs
									} else {
										badgeClass += ' kmb';  // KMB routes have longest stop IDs
									}
								}

								// Create span with badge styling
								const routeBadge = document.createElement("span");
								routeBadge.className = badgeClass;
								routeBadge.textContent = route;
								cell.appendChild(routeBadge);
								
							} else {
								cell.textContent = bookmark[i];
							}

							row.appendChild(cell);
						}
						const cell = document.createElement("td");
						const link = document.createElement("a");
						link.textContent = bookmark[3];
						link.href = "#";
						link.id = bookmark[4];
						link.onclick = function () {
							setCookies(bookmark[1], bookmark[2], bookmark[3], bookmark[4], bookmark[5], bookmark[6], bookmark[7], bookmark[8]);
							loadStopInfo(bookmark[1], bookmark[2], bookmark[3], bookmark[8]);
							loadstopeta(bookmark[4], bookmark[5], bookmark[1], bookmark[6], bookmark[7], bookmark[8]);
							stopRerunRequest();
							intervalID = setInterval(rerunRequest, rerunInterval);
							console.log("🔄 Auto-refresh started for bookmark:", bookmark[3], "- Interval ID:", intervalID);

							// Reset navigation state for bookmarks (no route context)
							currentRouteStops = [];
							currentStopIndex = -1;
							document.getElementById("prev-stop-btn").style.display = "none";
							document.getElementById("next-stop-btn").style.display = "none";
						};
						cell.appendChild(link);
						row.appendChild(cell);
						tbody.appendChild(row);
					});
					table.appendChild(tbody);
					tableContainer.appendChild(table);
				}

				function removeBookmark(bookmarkName) {
					localStorage.removeItem(bookmarkName);
				}

				function getCurrentDateTime() {
					const now = new Date();
					const year = now.getFullYear();
					const month = String(now.getMonth() + 1).padStart(2, '0');
					const day = String(now.getDate()).padStart(2, '0');
					const hours = String(now.getHours()).padStart(2, '0');
					const minutes = String(now.getMinutes()).padStart(2, '0');
					const seconds = String(now.getSeconds()).padStart(2, '0');
					const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
					return formattedDateTime;
				}

				async function getCurrentPosition() {
					var coords = null;
					await getPosition()
					.then((pos) => {
						coords = {
							latitude: pos.coords.latitude,
							longitude: pos.coords.longitude
						};
						// console.log(coords);
					})
					.catch((err) => {
						console.error(err.message);
					});
					return coords;
				}

				function getPosition() {
					return new Promise((res, rej) => {
						const options = {
							enableHighAccuracy: true,
							timeout: 10000,
							maximumAge: 30000,
						};
						navigator.geolocation.getCurrentPosition(res, rej, options);
					});
				}

				// Navigation functions for stop arrows
				function updateNavigationArrows() {
					const prevBtn = document.getElementById("prev-stop-btn");
					const nextBtn = document.getElementById("next-stop-btn");

					if (currentStopIndex === -1 || currentRouteStops.length === 0) {
						prevBtn.style.display = "none";
						nextBtn.style.display = "none";
						return;
					}

					// Show/hide left arrow (previous stop)
					if (currentStopIndex > 0) {
						prevBtn.style.display = "inline-block";
					} else {
						prevBtn.style.display = "none";
					}

					// Show/hide right arrow (next stop)
					if (currentStopIndex < currentRouteStops.length - 1) {
						nextBtn.style.display = "inline-block";
					} else {
						nextBtn.style.display = "none";
					}
				}

				function navigateToStop(direction) {
					if (currentRouteStops.length === 0 || currentStopIndex === -1) {
						console.warn("No route data available for navigation");
						return;
					}

					let newIndex = currentStopIndex + direction;

					// Check bounds
					if (newIndex < 0 || newIndex >= currentRouteStops.length) {
						console.warn("Cannot navigate beyond route bounds");
						return;
					}

					// Get the stop data
					const stopData = currentRouteStops[newIndex];
					const stop_id = stopData[0];
					const stop_name = stopData[1];
					const seq = stopData[2];

					// Update current index
					currentStopIndex = newIndex;

					// Load the new stop
					setCookies(currentRoute, currentDestTc, stop_name, stop_id, seq, currentBound, currentRouteId, currentCompany);
					loadStopInfo(currentRoute, currentDestTc, stop_name, currentCompany);
					loadstopeta(stop_id, seq, currentRoute, currentBound, currentRouteId, currentCompany);

					// Restart auto-refresh for new stop
					stopRerunRequest();
					intervalID = setInterval(rerunRequest, rerunInterval);
					console.log("🔄 Auto-refresh started for stop:", stop_name, "- Interval ID:", intervalID);

					// Update navigation arrows
					updateNavigationArrows();

					console.log(`📍 Navigated to stop ${newIndex + 1}/${currentRouteStops.length}: ${stop_name}`);
				}

				// Event listeners for navigation buttons
				document.getElementById("prev-stop-btn").addEventListener("click", function() {
					navigateToStop(-1); // Previous stop (seq - 1)
				});

				document.getElementById("next-stop-btn").addEventListener("click", function() {
					navigateToStop(1); // Next stop (seq + 1)
				});
			});
		</script>
	</body>
</html>
