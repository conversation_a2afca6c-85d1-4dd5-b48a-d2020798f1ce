#!/usr/bin/env python3
"""
Test script to check route 313 data and ETA
"""

import requests
import json

def test_route_313():
    print("Testing Route 313 NT data...")
    print("=" * 50)
    
    # Get route details for 313 in NT
    route_url = "https://data.etagmb.gov.hk/route/NT/313"
    try:
        response = requests.get(route_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("Route 313 NT API Response:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if 'data' in data and isinstance(data['data'], list):
                print(f"\nFound {len(data['data'])} route objects:")
                for i, route_obj in enumerate(data['data']):
                    route_id = route_obj.get('route_id')
                    directions = route_obj.get('directions', [])
                    print(f"\nRoute Object {i+1}:")
                    print(f"  route_id: {route_id}")
                    print(f"  directions: {len(directions)}")
                    
                    for j, direction in enumerate(directions):
                        route_seq = direction.get('route_seq')
                        dest_tc = direction.get('dest_tc')
                        print(f"    Direction {j+1}: route_seq={route_seq}, dest_tc={dest_tc}")
                        
                        # Test ETA for this route_id and stop
                        test_eta(route_id, "20001219", route_seq)
        else:
            print(f"Error getting route data: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"Error: {e}")

def test_eta(route_id, stop_id, route_seq):
    """Test ETA for specific route_id and stop_id"""
    eta_url = f"https://data.etagmb.gov.hk/eta/route-stop/{route_id}/{stop_id}"
    try:
        response = requests.get(eta_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            eta_data = data.get('data', [])
            print(f"      ETA Test (route_id={route_id}, stop_id={stop_id}):")
            print(f"        Status: {response.status_code}")
            print(f"        Data entries: {len(eta_data)}")
            
            for item in eta_data:
                item_route_seq = item.get('route_seq')
                enabled = item.get('enabled')
                eta_count = len(item.get('eta', []))
                print(f"          route_seq={item_route_seq}, enabled={enabled}, eta_entries={eta_count}")
                
                if item_route_seq == route_seq and enabled and eta_count > 0:
                    print(f"          ✅ FOUND WORKING ETA for route_seq {route_seq}!")
                    return True
        else:
            print(f"      ETA Test failed: {response.status_code}")
    except Exception as e:
        print(f"      ETA Test error: {e}")
    
    return False

if __name__ == "__main__":
    test_route_313()
